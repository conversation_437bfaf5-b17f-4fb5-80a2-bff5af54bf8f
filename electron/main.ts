import { app } from "electron";
import { fileURLToPath } from "node:url";
import path from "node:path";
import { ApplicationLifecycle, ApplicationConfig } from "../src/main/applicationLifecycle";
import { HealthMonitor } from "../src/main/healthMonitor";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, "..");

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
export const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
export const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, "public")
  : RENDERER_DIST;

// Application configuration
const config: ApplicationConfig = {
  isDevelopment: !!VITE_DEV_SERVER_URL,
  viteDevServerUrl: VITE_DEV_SERVER_URL,
  rendererDist: RENDERER_DIST,
  publicDir: process.env.VITE_PUBLIC,
  preloadPath: path.join(__dirname, "preload.mjs"),
};

// Global application instances
let applicationLifecycle: ApplicationLifecycle;
let healthMonitor: HealthMonitor;

/**
 * Main application entry point
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Electixir - Starting application...');
    console.log(`📊 Environment: ${config.isDevelopment ? 'Development' : 'Production'}`);
    console.log(`📁 App Root: ${process.env.APP_ROOT}`);
    console.log(`🌐 Renderer: ${config.isDevelopment ? config.viteDevServerUrl : config.rendererDist}`);

    // Initialize application lifecycle manager
    applicationLifecycle = new ApplicationLifecycle(config);
    await applicationLifecycle.initialize();

    // Initialize health monitoring
    healthMonitor = new HealthMonitor(applicationLifecycle);
    healthMonitor.start();

    console.log('✅ Electixir application started successfully');
  } catch (error) {
    console.error('❌ Failed to start Electixir application:', error);

    // Try to show error dialog
    try {
      const { dialog } = await import('electron');
      dialog.showErrorBox(
        'Electixir Startup Error',
        `Failed to start the application:\n\n${error}\n\nPlease check the logs and try again.`
      );
    } catch (dialogError) {
      console.error('Failed to show error dialog:', dialogError);
    }

    // Exit with error code
    app.exit(1);
  }
}

/**
 * Handle application shutdown
 */
async function shutdown(): Promise<void> {
  console.log('🛑 Shutting down Electixir application...');

  try {
    // Stop health monitoring
    if (healthMonitor) {
      healthMonitor.stop();
    }

    // Shutdown application lifecycle
    if (applicationLifecycle) {
      await applicationLifecycle.shutdown();
    }

    console.log('✅ Electixir application shutdown complete');
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    app.exit(1);
  }
}

// Handle process signals for graceful shutdown
process.on('SIGTERM', () => {
  console.log('📡 Received SIGTERM, shutting down gracefully...');
  shutdown().catch((error) => {
    console.error('Failed to shutdown gracefully:', error);
    app.exit(1);
  });
});

process.on('SIGINT', () => {
  console.log('📡 Received SIGINT, shutting down gracefully...');
  shutdown().catch((error) => {
    console.error('Failed to shutdown gracefully:', error);
    app.exit(1);
  });
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  console.log('🔒 Another instance is already running. Exiting...');
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    const mainWindow = applicationLifecycle?.getMainWindow();
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });

  // Start the application
  main().catch((error) => {
    console.error('❌ Unhandled error in main:', error);
    app.exit(1);
  });
}
