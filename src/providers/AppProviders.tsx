import { ReactNode, useEffect } from 'react'
import { QueryProvider } from './QueryProvider'
import { useAppStore } from '../stores/appStore'

interface AppProvidersProps {
  children: ReactNode
}

function NetworkStatusProvider({ children }: { children: ReactNode }) {
  const { setOnlineStatus } = useAppStore()

  useEffect(() => {
    // Set initial online status
    setOnlineStatus(navigator.onLine)

    // Listen for online/offline events
    const handleOnline = () => setOnlineStatus(true)
    const handleOffline = () => setOnlineStatus(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [setOnlineStatus])

  return <>{children}</>
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <QueryProvider>
      <NetworkStatusProvider>{children}</NetworkStatusProvider>
    </QueryProvider>
  )
}
