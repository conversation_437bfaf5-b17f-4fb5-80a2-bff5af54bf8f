import argon2 from 'argon2'
import crypto from 'crypto'

/**
 * Centralized cryptographic service for consistent password hashing and token generation
 * Eliminates duplication between UserRepository and UserService
 */
export class CryptoService {
  // Consistent Argon2 configuration across the application
  private static readonly ARGON2_CONFIG = {
    type: argon2.argon2id,
    memoryCost: 2 ** 16, // 64 MB
    timeCost: 3,         // 3 iterations
    parallelism: 1,      // 1 thread
  } as const

  /**
   * Hash a password using Argon2id with consistent configuration
   */
  static async hashPassword(password: string): Promise<string> {
    return await argon2.hash(password, this.ARGON2_CONFIG)
  }

  /**
   * Verify a password against its hash
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await argon2.verify(hash, password)
    } catch (error) {
      // Log error but don't expose details
      console.error('Password verification failed:', error)
      return false
    }
  }

  /**
   * Generate a secure session token
   */
  static generateSessionToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }

  /**
   * Generate a secure UUID
   */
  static generateId(): string {
    return crypto.randomUUID()
  }

  /**
   * Generate a secure random string of specified length
   */
  static generateRandomString(length: number = 32): string {
    return crypto.randomBytes(Math.ceil(length / 2)).toString('hex').slice(0, length)
  }
}
