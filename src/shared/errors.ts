/**
 * Domain error classes for consistent error handling across the application
 * Replaces plain Error usage with structured, typed errors
 */

export abstract class DomainError extends Error {
  abstract readonly code: string
  abstract readonly statusCode: number
  
  constructor(
    message: string,
    public readonly details?: Record<string, unknown>
  ) {
    super(message)
    this.name = this.constructor.name
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype)
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      details: this.details,
      statusCode: this.statusCode,
    }
  }
}

export class ValidationError extends DomainError {
  readonly code = 'VALIDATION_ERROR'
  readonly statusCode = 400

  constructor(message: string, details?: Record<string, unknown>) {
    super(message, details)
  }
}

export class NotFoundError extends DomainError {
  readonly code = 'NOT_FOUND'
  readonly statusCode = 404

  constructor(resource: string, identifier?: string) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`
    super(message, { resource, identifier })
  }
}

export class ConflictError extends DomainError {
  readonly code = 'CONFLICT'
  readonly statusCode = 409

  constructor(message: string, details?: Record<string, unknown>) {
    super(message, details)
  }
}

export class UnauthorizedError extends DomainError {
  readonly code = 'UNAUTHORIZED'
  readonly statusCode = 401

  constructor(message: string = 'Unauthorized access', details?: Record<string, unknown>) {
    super(message, details)
  }
}

export class ForbiddenError extends DomainError {
  readonly code = 'FORBIDDEN'
  readonly statusCode = 403

  constructor(message: string = 'Access forbidden', details?: Record<string, unknown>) {
    super(message, details)
  }
}

export class InternalError extends DomainError {
  readonly code = 'INTERNAL_ERROR'
  readonly statusCode = 500

  constructor(message: string = 'Internal server error', details?: Record<string, unknown>) {
    super(message, details)
  }
}

export class DatabaseError extends DomainError {
  readonly code = 'DATABASE_ERROR'
  readonly statusCode = 500

  constructor(operation: string, originalError?: Error) {
    super(`Database operation failed: ${operation}`, {
      operation,
      originalError: originalError?.message,
    })
  }
}

/**
 * Error factory for creating domain errors with consistent patterns
 */
export class ErrorFactory {
  static validation(message: string, details?: Record<string, unknown>): ValidationError {
    return new ValidationError(message, details)
  }

  static notFound(resource: string, identifier?: string): NotFoundError {
    return new NotFoundError(resource, identifier)
  }

  static conflict(message: string, details?: Record<string, unknown>): ConflictError {
    return new ConflictError(message, details)
  }

  static unauthorized(message?: string, details?: Record<string, unknown>): UnauthorizedError {
    return new UnauthorizedError(message, details)
  }

  static forbidden(message?: string, details?: Record<string, unknown>): ForbiddenError {
    return new ForbiddenError(message, details)
  }

  static internal(message?: string, details?: Record<string, unknown>): InternalError {
    return new InternalError(message, details)
  }

  static database(operation: string, originalError?: Error): DatabaseError {
    return new DatabaseError(operation, originalError)
  }

  /**
   * Convert unknown error to domain error
   */
  static fromUnknown(error: unknown, context?: string): DomainError {
    if (error instanceof DomainError) {
      return error
    }

    if (error instanceof Error) {
      return new InternalError(
        context ? `${context}: ${error.message}` : error.message,
        { originalError: error.message, stack: error.stack }
      )
    }

    return new InternalError(
      context ? `${context}: Unknown error` : 'Unknown error occurred',
      { originalError: String(error) }
    )
  }
}

/**
 * Type guard to check if error is a domain error
 */
export function isDomainError(error: unknown): error is DomainError {
  return error instanceof DomainError
}

/**
 * Extract error details for logging/debugging
 */
export function getErrorDetails(error: unknown): {
  code: string
  message: string
  statusCode: number
  details?: Record<string, unknown>
} {
  if (isDomainError(error)) {
    return {
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      details: error.details,
    }
  }

  if (error instanceof Error) {
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message,
      statusCode: 500,
      details: { stack: error.stack },
    }
  }

  return {
    code: 'UNKNOWN_ERROR',
    message: String(error),
    statusCode: 500,
  }
}
