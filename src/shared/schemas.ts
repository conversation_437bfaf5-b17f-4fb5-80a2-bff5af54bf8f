import { z } from 'zod'

// Common field schemas - reusable across entities
export const CommonSchemas = {
  id: z.string().uuid(),
  timestamp: z.string(),
  syncStatus: z.enum(['pending', 'synced', 'conflict']),
  pagination: z.object({
    limit: z.number().int().min(1).max(1000).optional(),
    offset: z.number().int().min(0).optional(),
  }),
}

// User schemas
export const UserSchemas = {
  base: z.object({
    id: CommonSchemas.id,
    username: z
      .string()
      .min(3)
      .max(50)
      .regex(
        /^[a-zA-Z0-9_]+$/,
        'Username can only contain letters, numbers, and underscores'
      ),
    role: z.enum(['admin', 'operator', 'viewer']),
    isActive: z.boolean(),
    createdAt: CommonSchemas.timestamp,
    updatedAt: CommonSchemas.timestamp,
  }),

  create: z.object({
    username: z
      .string()
      .min(3)
      .max(50)
      .regex(/^[a-zA-Z0-9_]+$/),
    password: z.string().min(8),
    role: z.enum(['admin', 'operator', 'viewer']),
  }),

  update: z.object({
    id: CommonSchemas.id,
    role: z.enum(['admin', 'operator', 'viewer']).optional(),
    isActive: z.boolean().optional(),
  }),

  authenticate: z.object({
    username: z.string().min(1),
    password: z.string().min(1),
  }),

  changePassword: z.object({
    userId: CommonSchemas.id,
    currentPassword: z.string().min(1),
    newPassword: z.string().min(8),
  }),
}

// Voter schemas
export const VoterSchemas = {
  base: z.object({
    id: CommonSchemas.id,
    pollingStationId: CommonSchemas.id,
    sectionId: CommonSchemas.id,
    name: z.string().min(1),
    epicNumber: z
      .string()
      .regex(/^[A-Z]{3}[0-9A-Z]{7}$/i, 'Invalid EPIC format'),
    houseNumber: z.string().min(1),
    birthYear: z.number().int().min(1900).max(new Date().getFullYear()),
    gender: z.string().min(1),
    relationType: z.string().min(1),
    status: z.string().min(1),
    phone: z.string().nullable(),
    email: z.string().email().nullable(),
    facebook: z.string().nullable(),
    instagram: z.string().nullable(),
    twitter: z.string().nullable(),
    supporterStatus: z.string().nullable(),
    education: z.string().nullable(),
    occupation: z.string().nullable(),
    community: z.string().nullable(),
    religion: z.string().nullable(),
    economicStatus: z.string().nullable(),
    customNotes: z.string().nullable(),
    relationName: z.string().nullable(),
    createdAt: CommonSchemas.timestamp,
    updatedAt: CommonSchemas.timestamp,
    syncStatus: CommonSchemas.syncStatus,
  }),

  create: z.object({
    stationId: CommonSchemas.id,
    sectionId: CommonSchemas.id,
    name: z.string().min(1),
    epicNumber: z.string().regex(/^[A-Z]{3}[0-9A-Z]{7}$/i),
    houseNumber: z.string().min(1),
    birthYear: z.number().int().min(1900).max(new Date().getFullYear()),
    gender: z.string().min(1),
    relationType: z.string().min(1),
    status: z.string().min(1),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    facebook: z.string().optional(),
    instagram: z.string().optional(),
    twitter: z.string().optional(),
    supporterStatus: z.string().optional(),
    education: z.string().optional(),
    occupation: z.string().optional(),
    community: z.string().optional(),
    religion: z.string().optional(),
    economicStatus: z.string().optional(),
    customNotes: z.string().optional(),
    relationName: z.string().optional(),
  }),

  update: z.object({
    name: z.string().min(1).optional(),
    epicNumber: z
      .string()
      .regex(/^[A-Z]{3}[0-9A-Z]{7}$/i)
      .optional(),
    houseNumber: z.string().min(1).optional(),
    birthYear: z
      .number()
      .int()
      .min(1900)
      .max(new Date().getFullYear())
      .optional(),
    gender: z.string().min(1).optional(),
    relationType: z.string().min(1).optional(),
    status: z.string().min(1).optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    facebook: z.string().optional(),
    instagram: z.string().optional(),
    twitter: z.string().optional(),
    supporterStatus: z.string().optional(),
    education: z.string().optional(),
    occupation: z.string().optional(),
    community: z.string().optional(),
    religion: z.string().optional(),
    economicStatus: z.string().optional(),
    customNotes: z.string().optional(),
    relationName: z.string().optional(),
  }),

  filters: z.object({
    status: z.string().optional(),
    gender: z.string().optional(),
    sectionId: CommonSchemas.id.optional(),
    stationId: CommonSchemas.id.optional(),
    birthYearFrom: z.number().int().optional(),
    birthYearTo: z.number().int().optional(),
    ...CommonSchemas.pagination.shape,
  }),
}

// Section schemas
export const SectionSchemas = {
  base: z.object({
    id: CommonSchemas.id,
    stationId: CommonSchemas.id,
    sectionName: z.string().min(1),
    sectionNumber: z.string().min(1),
    createdAt: CommonSchemas.timestamp,
    updatedAt: CommonSchemas.timestamp,
    syncStatus: CommonSchemas.syncStatus,
  }),

  create: z.object({
    stationId: CommonSchemas.id,
    sectionName: z.string().min(1),
    sectionNumber: z.string().min(1),
  }),

  update: z.object({
    id: CommonSchemas.id,
    sectionName: z.string().min(1).optional(),
    sectionNumber: z.string().min(1).optional(),
  }),
}

// Station schemas
export const StationSchemas = {
  base: z.object({
    id: CommonSchemas.id,
    stationName: z.string().min(1),
    stationNumber: z.string().min(1),
    createdAt: CommonSchemas.timestamp,
    updatedAt: CommonSchemas.timestamp,
    syncStatus: CommonSchemas.syncStatus,
  }),

  create: z.object({
    stationName: z.string().min(1),
    stationNumber: z.string().min(1),
  }),

  update: z.object({
    id: CommonSchemas.id,
    stationName: z.string().min(1).optional(),
    stationNumber: z.string().min(1).optional(),
  }),
}

// Transaction schemas
export const TransactionSchemas = {
  base: z.object({
    id: CommonSchemas.id,
    voterId: CommonSchemas.id,
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    purpose: z.string().min(1),
    amount: z.string().regex(/^\d+\.\d{2}$/),
    createdAt: CommonSchemas.timestamp,
    updatedAt: CommonSchemas.timestamp,
    syncStatus: CommonSchemas.syncStatus,
  }),

  create: z.object({
    voterId: CommonSchemas.id,
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    purpose: z.string().min(1),
    amount: z.string().regex(/^\d+\.\d{2}$/),
  }),

  update: z.object({
    id: CommonSchemas.id,
    date: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/)
      .optional(),
    purpose: z.string().min(1).optional(),
    amount: z
      .string()
      .regex(/^\d+\.\d{2}$/)
      .optional(),
  }),
}

// Export types for use in other files
export type User = z.infer<typeof UserSchemas.base>
export type CreateUserInput = z.infer<typeof UserSchemas.create>
export type UpdateUserInput = z.infer<typeof UserSchemas.update>

export type Voter = z.infer<typeof VoterSchemas.base>
export type CreateVoterInput = z.infer<typeof VoterSchemas.create>
export type UpdateVoterInput = z.infer<typeof VoterSchemas.update>
export type VoterFilters = z.infer<typeof VoterSchemas.filters>

export type Section = z.infer<typeof SectionSchemas.base>
export type CreateSectionInput = z.infer<typeof SectionSchemas.create>
export type UpdateSectionInput = z.infer<typeof SectionSchemas.update>

export type Station = z.infer<typeof StationSchemas.base>
export type CreateStationInput = z.infer<typeof StationSchemas.create>
export type UpdateStationInput = z.infer<typeof StationSchemas.update>

export type Transaction = z.infer<typeof TransactionSchemas.base>
export type CreateTransactionInput = z.infer<typeof TransactionSchemas.create>
export type UpdateTransactionInput = z.infer<typeof TransactionSchemas.update>
