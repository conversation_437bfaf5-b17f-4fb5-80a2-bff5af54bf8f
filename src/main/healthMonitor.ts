import { getSQLite } from '@/db'
import { ApplicationLifecycle } from '@/main/applicationLifecycle'

export interface HealthCheck {
  name: string
  status: 'healthy' | 'unhealthy' | 'unknown'
  message?: string
  lastChecked: Date
  responseTime?: number
}

export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy'
  checks: HealthCheck[]
  uptime: number
  memoryUsage: NodeJS.MemoryUsage
  timestamp: Date
}

export class HealthMonitor {
  private startTime: Date
  private checkInterval: NodeJS.Timeout | null = null
  private lastHealthStatus: SystemHealth | null = null

  constructor(
    private applicationLifecycle: ApplicationLifecycle,
    private intervalMs: number = 30000 // 30 seconds
  ) {
    this.startTime = new Date()
  }

  /**
   * Start health monitoring
   */
  start(): void {
    if (this.checkInterval) {
      return // Already started
    }

    console.log('🏥 Starting health monitor...')

    // Run initial health check
    this.performHealthCheck().catch((error) => {
      console.error('Initial health check failed:', error)
    })

    // Set up periodic health checks
    this.checkInterval = setInterval(() => {
      this.performHealthCheck().catch((error) => {
        console.error('Periodic health check failed:', error)
      })
    }, this.intervalMs)

    console.log(`✅ Health monitor started (interval: ${this.intervalMs}ms)`)
  }

  /**
   * Stop health monitoring
   */
  stop(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
      console.log('🛑 Health monitor stopped')
    }
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<SystemHealth> {
    // const startTime = Date.now()
    const checks: HealthCheck[] = []

    // Check application lifecycle
    checks.push(await this.checkApplicationLifecycle())

    // Check database connectivity
    checks.push(await this.checkDatabaseHealth())

    // Check memory usage
    checks.push(await this.checkMemoryUsage())

    // Check main window status
    checks.push(await this.checkMainWindowHealth())

    // Determine overall health
    const unhealthyChecks = checks.filter(
      (check) => check.status === 'unhealthy'
    )
    const unknownChecks = checks.filter((check) => check.status === 'unknown')

    let overall: SystemHealth['overall']
    if (unhealthyChecks.length > 0) {
      overall = 'unhealthy'
    } else if (unknownChecks.length > 0) {
      overall = 'degraded'
    } else {
      overall = 'healthy'
    }

    const health: SystemHealth = {
      overall,
      checks,
      uptime: Date.now() - this.startTime.getTime(),
      memoryUsage: process.memoryUsage(),
      timestamp: new Date(),
    }

    this.lastHealthStatus = health

    // Log health status changes
    if (this.shouldLogHealthStatus(health)) {
      this.logHealthStatus(health)
    }

    return health
  }

  /**
   * Check application lifecycle health
   */
  private async checkApplicationLifecycle(): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      const status = this.applicationLifecycle.getHealthStatus()

      if (!status.isInitialized) {
        return {
          name: 'application_lifecycle',
          status: 'unhealthy',
          message: 'Application not initialized',
          lastChecked: new Date(),
          responseTime: Date.now() - startTime,
        }
      }

      if (status.isShuttingDown) {
        return {
          name: 'application_lifecycle',
          status: 'degraded',
          message: 'Application is shutting down',
          lastChecked: new Date(),
          responseTime: Date.now() - startTime,
        }
      }

      return {
        name: 'application_lifecycle',
        status: 'healthy',
        message: 'Application lifecycle is healthy',
        lastChecked: new Date(),
        responseTime: Date.now() - startTime,
      }
    } catch (error) {
      return {
        name: 'application_lifecycle',
        status: 'unhealthy',
        message: `Application lifecycle check failed: ${error}`,
        lastChecked: new Date(),
        responseTime: Date.now() - startTime,
      }
    }
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      const sqlite = await getSQLite()

      // Simple query to test database connectivity
      const result = sqlite.prepare('SELECT 1 as test').get() as
        | { test: number }
        | undefined

      if (result?.test === 1) {
        return {
          name: 'database',
          status: 'healthy',
          message: 'Database is responsive',
          lastChecked: new Date(),
          responseTime: Date.now() - startTime,
        }
      } else {
        return {
          name: 'database',
          status: 'unhealthy',
          message: 'Database query returned unexpected result',
          lastChecked: new Date(),
          responseTime: Date.now() - startTime,
        }
      }
    } catch (error) {
      return {
        name: 'database',
        status: 'unhealthy',
        message: `Database health check failed: ${error}`,
        lastChecked: new Date(),
        responseTime: Date.now() - startTime,
      }
    }
  }

  /**
   * Check memory usage
   */
  private async checkMemoryUsage(): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      const memUsage = process.memoryUsage()
      const heapUsedMB = memUsage.heapUsed / 1024 / 1024
      const heapTotalMB = memUsage.heapTotal / 1024 / 1024
      const rssMB = memUsage.rss / 1024 / 1024

      // Define thresholds (in MB)
      const WARNING_HEAP_MB = 200
      const CRITICAL_HEAP_MB = 500
      const WARNING_RSS_MB = 300
      const CRITICAL_RSS_MB = 800

      let status: HealthCheck['status'] = 'healthy'
      let message = `Heap: ${heapUsedMB.toFixed(1)}MB/${heapTotalMB.toFixed(1)}MB, RSS: ${rssMB.toFixed(1)}MB`

      if (heapUsedMB > CRITICAL_HEAP_MB || rssMB > CRITICAL_RSS_MB) {
        status = 'unhealthy'
        message = `Critical memory usage - ${message}`
      } else if (heapUsedMB > WARNING_HEAP_MB || rssMB > WARNING_RSS_MB) {
        status = 'degraded'
        message = `High memory usage - ${message}`
      }

      return {
        name: 'memory_usage',
        status,
        message,
        lastChecked: new Date(),
        responseTime: Date.now() - startTime,
      }
    } catch (error) {
      return {
        name: 'memory_usage',
        status: 'unknown',
        message: `Memory usage check failed: ${error}`,
        lastChecked: new Date(),
        responseTime: Date.now() - startTime,
      }
    }
  }

  /**
   * Check main window health
   */
  private async checkMainWindowHealth(): Promise<HealthCheck> {
    const startTime = Date.now()

    try {
      const mainWindow = this.applicationLifecycle.getMainWindow()

      if (!mainWindow) {
        return {
          name: 'main_window',
          status: 'degraded',
          message: 'Main window not created',
          lastChecked: new Date(),
          responseTime: Date.now() - startTime,
        }
      }

      if (mainWindow.isDestroyed()) {
        return {
          name: 'main_window',
          status: 'unhealthy',
          message: 'Main window is destroyed',
          lastChecked: new Date(),
          responseTime: Date.now() - startTime,
        }
      }

      return {
        name: 'main_window',
        status: 'healthy',
        message: 'Main window is healthy',
        lastChecked: new Date(),
        responseTime: Date.now() - startTime,
      }
    } catch (error) {
      return {
        name: 'main_window',
        status: 'unknown',
        message: `Main window check failed: ${error}`,
        lastChecked: new Date(),
        responseTime: Date.now() - startTime,
      }
    }
  }

  /**
   * Determine if health status should be logged
   */
  private shouldLogHealthStatus(health: SystemHealth): boolean {
    // Always log if this is the first check
    if (!this.lastHealthStatus) {
      return true
    }

    // Log if overall status changed
    if (this.lastHealthStatus.overall !== health.overall) {
      return true
    }

    // Log if any individual check status changed
    const previousChecks = new Map(
      this.lastHealthStatus.checks.map((check) => [check.name, check.status])
    )
    const currentChecks = new Map(
      health.checks.map((check) => [check.name, check.status])
    )

    for (const [name, status] of currentChecks) {
      if (previousChecks.get(name) !== status) {
        return true
      }
    }

    return false
  }

  /**
   * Log health status
   */
  private logHealthStatus(health: SystemHealth): void {
    const emoji =
      health.overall === 'healthy'
        ? '✅'
        : health.overall === 'degraded'
          ? '⚠️'
          : '❌'
    console.log(`${emoji} System Health: ${health.overall.toUpperCase()}`)

    health.checks.forEach((check) => {
      const checkEmoji =
        check.status === 'healthy'
          ? '✅'
          : check.status === 'degraded'
            ? '⚠️'
            : '❌'
      console.log(
        `  ${checkEmoji} ${check.name}: ${check.status} (${check.responseTime}ms) - ${check.message}`
      )
    })

    const memMB = health.memoryUsage.heapUsed / 1024 / 1024
    console.log(
      `📊 Uptime: ${Math.floor(health.uptime / 1000)}s, Memory: ${memMB.toFixed(1)}MB`
    )
  }

  /**
   * Get the last health status
   */
  getLastHealthStatus(): SystemHealth | null {
    return this.lastHealthStatus
  }

  /**
   * Get current uptime in milliseconds
   */
  getUptime(): number {
    return Date.now() - this.startTime.getTime()
  }
}
