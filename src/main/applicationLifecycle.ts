import { app, BrowserWindow } from 'electron'
import { getDB, closeDB } from '@/db'
import { initIpcHandlers } from '@/ipc/handlers'
import path from 'node:path'
// import { fileURLToPath } from 'node:url'

// const __dirname = path.dirname(fileURLToPath(import.meta.url))

export interface ApplicationConfig {
  isDevelopment: boolean
  viteDevServerUrl?: string
  rendererDist: string
  publicDir: string
  preloadPath: string
}

export class ApplicationLifecycle {
  private mainWindow: BrowserWindow | null = null
  private isInitialized = false
  private isShuttingDown = false

  constructor(private config: ApplicationConfig) {}

  /**
   * Initialize the application with proper error handling
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Application already initialized')
    }

    try {
      console.log('🚀 Starting Electixir application...')

      // Step 1: Initialize database and run migrations
      console.log('📊 Initializing database...')
      await this.initializeDatabase()

      // Step 2: Initialize IPC handlers
      console.log('🔗 Initializing IPC handlers...')
      await this.initializeIpcHandlers()

      // Step 3: Set up application event handlers
      console.log('⚡ Setting up application event handlers...')
      this.setupApplicationEventHandlers()

      // Step 4: Create main window when ready
      console.log('🪟 Setting up window creation...')
      this.setupWindowCreation()

      this.isInitialized = true
      console.log('✅ Application initialization complete')
    } catch (error) {
      console.error('❌ Application initialization failed:', error)
      await this.handleInitializationError(error)
      throw error
    }
  }

  /**
   * Initialize database with proper error handling
   */
  private async initializeDatabase(): Promise<void> {
    try {
      await getDB()
      console.log('✅ Database initialized successfully')
    } catch (error) {
      console.error('❌ Database initialization failed:', error)
      throw new Error(`Database initialization failed: ${error}`)
    }
  }

  /**
   * Initialize IPC handlers
   */
  private async initializeIpcHandlers(): Promise<void> {
    try {
      initIpcHandlers()
      console.log('✅ IPC handlers initialized successfully')
    } catch (error) {
      console.error('❌ IPC handlers initialization failed:', error)
      throw new Error(`IPC handlers initialization failed: ${error}`)
    }
  }

  /**
   * Set up application-level event handlers
   */
  private setupApplicationEventHandlers(): void {
    // Handle application ready
    app.whenReady().then(() => {
      this.createMainWindow()
    })

    // Handle window closure
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.shutdown()
      }
    })

    // Handle application activation (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow()
      }
    })

    // Handle before quit
    app.on('before-quit', async (event) => {
      if (!this.isShuttingDown) {
        event.preventDefault()
        await this.shutdown()
      }
    })

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error)
      this.handleCriticalError(error)
    })

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
      this.handleCriticalError(new Error(`Unhandled Rejection: ${reason}`))
    })
  }

  /**
   * Set up window creation logic
   */
  private setupWindowCreation(): void {
    // Window creation is handled by the 'ready' event
  }

  /**
   * Create the main application window
   */
  private createMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.focus()
      return
    }

    console.log('🪟 Creating main window...')

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Don't show until ready
      icon: path.join(this.config.publicDir, 'electron-vite.svg'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: this.config.preloadPath,
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    })

    // Handle window ready to show
    this.mainWindow.once('ready-to-show', () => {
      console.log('✅ Main window ready to show')
      this.mainWindow?.show()

      if (this.config.isDevelopment) {
        this.mainWindow?.webContents.openDevTools()
      }
    })

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })

    // Handle navigation
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl)

      // Only allow navigation to the same origin in production
      if (
        !this.config.isDevelopment &&
        parsedUrl.origin !== this.config.viteDevServerUrl
      ) {
        event.preventDefault()
      }
    })

    // Load the application
    this.loadApplication()
  }

  /**
   * Load the application content
   */
  private loadApplication(): void {
    if (!this.mainWindow) return

    if (this.config.viteDevServerUrl) {
      console.log(
        '🔧 Loading development server:',
        this.config.viteDevServerUrl
      )
      this.mainWindow.loadURL(this.config.viteDevServerUrl)
    } else {
      const indexPath = path.join(this.config.rendererDist, 'index.html')
      console.log('📦 Loading production build:', indexPath)
      this.mainWindow.loadFile(indexPath)
    }
  }

  /**
   * Handle initialization errors
   */
  private async handleInitializationError(error: unknown): Promise<void> {
    console.error('🚨 Critical initialization error:', error)

    // Try to show error dialog if possible
    try {
      const { dialog } = await import('electron')
      dialog.showErrorBox(
        'Application Initialization Failed',
        `The application failed to start properly:\n\n${error}\n\nPlease check the logs and try again.`
      )
    } catch (dialogError) {
      console.error('Failed to show error dialog:', dialogError)
    }

    // Attempt cleanup
    await this.cleanup()

    // Exit the application
    app.exit(1)
  }

  /**
   * Handle critical runtime errors
   */
  private handleCriticalError(error: Error): void {
    console.error('🚨 Critical runtime error:', error)

    // In development, we might want to keep the app running
    if (this.config.isDevelopment) {
      console.log('🔧 Development mode: continuing after error')
      return
    }

    // In production, gracefully shutdown
    this.shutdown().catch((shutdownError) => {
      console.error('Failed to shutdown gracefully:', shutdownError)
      app.exit(1)
    })
  }

  /**
   * Graceful application shutdown
   */
  async shutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return
    }

    this.isShuttingDown = true
    console.log('🛑 Shutting down application...')

    try {
      // Close main window
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.close()
        this.mainWindow = null
      }

      // Cleanup resources
      await this.cleanup()

      console.log('✅ Application shutdown complete')
      app.quit()
    } catch (error) {
      console.error('❌ Error during shutdown:', error)
      app.exit(1)
    }
  }

  /**
   * Cleanup application resources
   */
  private async cleanup(): Promise<void> {
    try {
      // Close database connections
      closeDB()
      console.log('✅ Database connections closed')
    } catch (error) {
      console.error('❌ Error closing database:', error)
    }
  }

  /**
   * Get application health status
   */
  getHealthStatus(): {
    isInitialized: boolean
    isShuttingDown: boolean
    hasMainWindow: boolean
    databaseConnected: boolean
  } {
    return {
      isInitialized: this.isInitialized,
      isShuttingDown: this.isShuttingDown,
      hasMainWindow: this.mainWindow !== null && !this.mainWindow.isDestroyed(),
      databaseConnected: true, // TODO: Add actual database health check
    }
  }

  /**
   * Get the main window instance
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }
}
