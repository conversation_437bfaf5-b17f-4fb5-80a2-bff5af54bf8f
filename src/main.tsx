import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import { Theme } from '@radix-ui/themes'
import App from '@/App.tsx'
import '@/index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Theme>
      <App />
    </Theme>
  </React.StrictMode>
)

// Use contextBridge (only in Electron)
if (typeof window !== 'undefined' && window.ipcRenderer) {
  window.ipcRenderer.on('main-process-message', (_event, message) => {
    console.log(message)
  })
}
