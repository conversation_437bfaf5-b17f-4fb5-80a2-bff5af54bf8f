import React from 'react'
import { Box, Flex, Text, Card } from '@radix-ui/themes'
import { LeftSidebar } from '../components/layout/LeftSidebar'
import { useStationsAndSections } from '../hooks/useStationsAndSections'

export function StationTreeDemo() {
  const { stations, sections, loading, error } = useStationsAndSections()

  const handleSelectionChange = (
    selectedStations: string[],
    selectedSections: string[]
  ) => {
    console.log('Selected stations:', selectedStations)
    console.log('Selected sections:', selectedSections)
  }

  if (loading) {
    return (
      <Flex align="center" justify="center" height="100vh">
        <Text>Loading stations and sections...</Text>
      </Flex>
    )
  }

  if (error) {
    return (
      <Flex align="center" justify="center" height="100vh">
        <Card>
          <Text color="red">Error: {error}</Text>
        </Card>
      </Flex>
    )
  }

  return (
    <Flex height="100vh">
      {/* Left Sidebar */}
      <LeftSidebar
        stations={stations}
        sections={sections}
        onSelectionChange={handleSelectionChange}
      />

      {/* Main Content Area */}
      <Box flexGrow="1" p="4">
        <Text size="6" weight="bold" mb="4">
          Station Tree Demo
        </Text>

        <Card>
          <Text size="3" mb="3">
            Instructions:
          </Text>
          <ul style={{ paddingLeft: '20px' }}>
            <li>Click the chevron arrows to expand/collapse stations</li>
            <li>Check a station to select all its sections</li>
            <li>Uncheck a station to deselect all its sections</li>
            <li>
              Individual section selection will update the station's state
            </li>
            <li>Check the browser console to see selection changes</li>
          </ul>
        </Card>

        <Card mt="4">
          <Text size="3" mb="3">
            Data Summary:
          </Text>
          <Text size="2">
            Stations: {stations.length} | Sections: {sections.length}
          </Text>
        </Card>
      </Box>
    </Flex>
  )
}
