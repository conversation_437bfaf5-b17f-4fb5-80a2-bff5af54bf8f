import { useState } from 'react'
import { CustomTitleBar } from '@/components/layout/CustomTitleBar'
import { StatusBar } from '@/components/layout/StatusBar'
import { LeftSidebar } from '@/components/layout/LeftSidebar'
import { MainContent } from '@/components/layout/MainContent'
import styles from './MainPage.module.css'

interface MainPageProps {
  user?: {
    id: string
    username: string
    role: string
  } | null
  onLogout?: () => void
}

export function MainPage({ user }: MainPageProps) {
  const [selectedSectionId, setSelectedSectionId] = useState<string>('')
  const [currentView, setCurrentView] = useState<
    'voters' | 'reports' | 'config'
  >('voters')

  // Mock data - will be replaced with real data
  const mockPollingStations = [
    {
      id: '1',
      name: 'Polling Station 1',
      sections: [
        { id: '1-1', name: 'Section Name 1' },
        { id: '1-2', name: 'Section Name 2' },
      ],
    },
    {
      id: '2',
      name: 'Polling Station 2',
      sections: [
        { id: '2-1', name: 'Section Name 1' },
        { id: '2-2', name: 'Section Name 2' },
      ],
    },
  ]

  return (
    <div className={styles.container}>
      {/* Custom Title Bar */}
      <div className={styles.titleBar}>
        <CustomTitleBar title="Electixir - Electoral Management System" />
      </div>

      {/* Main Layout */}
      <div className={styles.mainLayout}>
        {/* Static Sidebar */}
        <div className={styles.sidebar}>
          <LeftSidebar
            pollingStations={mockPollingStations}
            selectedSectionId={selectedSectionId}
            onSectionSelect={setSelectedSectionId}
          />
        </div>

        {/* Main Content Area */}
        <div className={styles.content}>
          <MainContent
            currentView={currentView}
            onViewChange={(view) =>
              setCurrentView(view as 'voters' | 'reports' | 'config')
            }
          />
        </div>
      </div>

      {/* Status Bar */}
      <div className={styles.statusBar}>
        <StatusBar
          selectedCount={0}
          totalCount={100}
          status={`Logged in as ${user?.username || 'Unknown'} (${user?.role || 'Unknown'})`}
        />
      </div>
    </div>
  )
}
