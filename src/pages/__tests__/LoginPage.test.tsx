import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { LoginPage } from '../LoginPage'

describe('LoginPage', () => {
  it('renders login form', () => {
    render(<LoginPage />)

    expect(screen.getByText('Electixir')).toBeInTheDocument()
    expect(screen.getByText('Electoral Management System')).toBeInTheDocument()
    expect(screen.getByLabelText('Username')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument()
  })

  it('calls onLogin with credentials when form is submitted', async () => {
    const mockOnLogin = vi.fn()
    render(<LoginPage onLogin={mockOnLogin} />)

    const usernameInput = screen.getByLabelText('Username')
    const passwordInput = screen.getByLabelText('Password')
    const submitButton = screen.getByRole('button', { name: 'Sign In' })

    fireEvent.change(usernameInput, { target: { value: 'testuser' } })
    fireEvent.change(passwordInput, { target: { value: 'testpass' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockOnLogin).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'testpass'
      })
    })
  })

  it('displays error message when provided', () => {
    render(<LoginPage error="Invalid credentials" />)
    expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
  })

  it('disables form when loading', () => {
    render(<LoginPage loading={true} />)

    expect(screen.getByLabelText('Username')).toBeDisabled()
    expect(screen.getByLabelText('Password')).toBeDisabled()
    expect(screen.getByRole('button', { name: 'Signing in...' })).toBeDisabled()
  })
})