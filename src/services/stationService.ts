import { z } from 'zod'
import crypto from 'crypto'
import type { StationRepository } from '@/repositories/stationRepository'

// Input validation schemas
const CreateStationInput = z.object({
  stationName: z.string().min(1),
  stationNumber: z.string().min(1),
})

const UpdateStationInput = z.object({
  stationName: z.string().min(1).optional(),
  stationNumber: z.string().min(1).optional(),
})

const StationFilters = z.object({
  syncStatus: z.string().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

export class StationService {
  constructor(private stationRepository: StationRepository) {}

  async createStation(input: z.infer<typeof CreateStationInput>) {
    const data = CreateStationInput.parse(input)

    // Check for unique number
    const existing = await this.stationRepository.findByNumber(
      data.stationNumber
    )
    if (existing) {
      throw new Error(
        `Station with number '${data.stationNumber}' already exists`
      )
    }

    const now = new Date().toISOString()
    const id = crypto.randomUUID()

    return await this.stationRepository.create({
      id,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
      stationName: data.stationName,
      stationNumber: data.stationNumber,
    })
  }

  async getStationById(id: string) {
    return await this.stationRepository.getById(id)
  }

  async getStationByNumber(stationNumber: string) {
    return await this.stationRepository.findByNumber(stationNumber)
  }

  async updateStation(id: string, patch: z.infer<typeof UpdateStationInput>) {
    const data = UpdateStationInput.parse(patch)

    // Check for unique number if updating number
    if (data.stationNumber) {
      const existing = await this.stationRepository.findByNumber(
        data.stationNumber
      )
      if (existing && existing.id !== id) {
        throw new Error(
          `Station with number '${data.stationNumber}' already exists`
        )
      }
    }

    return await this.stationRepository.update(id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })
  }

  async deleteStation(id: string) {
    const when = new Date().toISOString()
    return await this.stationRepository.softDelete(id, when)
  }

  async cascadeDeleteStation(id: string) {
    const when = new Date().toISOString()
    return await this.stationRepository.cascadeDelete(id, when)
  }

  async listStations(filters: z.infer<typeof StationFilters> = {}) {
    const validated = StationFilters.parse(filters)
    return await this.stationRepository.findMany(validated)
  }

  async searchStationsByName(searchTerm: string) {
    return await this.stationRepository.searchByName(searchTerm)
  }

  async getStationWithSections(id: string) {
    return await this.stationRepository.getWithSections(id)
  }

  async getStationWithVoterCount(id: string) {
    return await this.stationRepository.getWithVoterCount(id)
  }
}
