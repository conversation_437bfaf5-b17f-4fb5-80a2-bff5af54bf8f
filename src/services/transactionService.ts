import { z } from 'zod'
import crypto from 'crypto'
import type { TransactionRepository } from '@/repositories/transactionRepository'

// Input validation schemas
const CreateTransactionInput = z.object({
  voterId: z.string().uuid(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
  purpose: z.string().min(1),
  amount: z.string().regex(/^\d+\.\d{2}$/), // Decimal string format
})

const UpdateTransactionInput = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  purpose: z.string().min(1).optional(),
  amount: z.string().regex(/^\d+\.\d{2}$/).optional(),
})

const TransactionFilters = z.object({
  syncStatus: z.string().optional(),
  voterId: z.string().uuid().optional(),
  dateFrom: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  dateTo: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  purpose: z.string().optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

export class TransactionService {
  constructor(private transactionRepository: TransactionRepository) {}

  async createTransaction(input: z.infer<typeof CreateTransactionInput>) {
    const data = CreateTransactionInput.parse(input)

    // Validate amount format
    const amount = parseFloat(data.amount)
    if (isNaN(amount) || amount <= 0) {
      throw new Error('Invalid amount format')
    }

    const now = new Date().toISOString()
    const id = crypto.randomUUID()

    return await this.transactionRepository.create({
      id,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
      voterId: data.voterId,
      date: data.date,
      purpose: data.purpose,
      amount: data.amount,
    })
  }

  async getTransactionById(id: string) {
    return await this.transactionRepository.getById(id)
  }

  async updateTransaction(id: string, patch: z.infer<typeof UpdateTransactionInput>) {
    const data = UpdateTransactionInput.parse(patch)

    // Validate amount format if provided
    if (data.amount) {
      const amount = parseFloat(data.amount)
      if (isNaN(amount) || amount <= 0) {
        throw new Error('Invalid amount format')
      }
    }

    return await this.transactionRepository.update(id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })
  }

  async deleteTransaction(id: string) {
    const when = new Date().toISOString()
    return await this.transactionRepository.softDelete(id, when)
  }

  async listTransactions(filters: z.infer<typeof TransactionFilters> = {}) {
    const validated = TransactionFilters.parse(filters)
    return await this.transactionRepository.findMany(validated)
  }

  async getTransactionsByVoter(voterId: string) {
    return await this.transactionRepository.findByVoter(voterId)
  }

  async getTransactionsByDateRange(dateFrom: string, dateTo: string) {
    return await this.transactionRepository.findByDateRange(dateFrom, dateTo)
  }

  async searchTransactionsByPurpose(purposeSearch: string) {
    return await this.transactionRepository.findByPurpose(purposeSearch)
  }

  async getTransactionHistory(voterId: string) {
    return await this.transactionRepository.getTransactionHistory(voterId)
  }

  async getTotalAmountByVoter(voterId: string) {
    return await this.transactionRepository.getTotalAmountByVoter(voterId)
  }

  async getTotalAmountByPurpose(purposeSearch: string) {
    return await this.transactionRepository.getTotalAmountByPurpose(purposeSearch)
  }

  async getTotalAmountByDateRange(dateFrom: string, dateTo: string) {
    return await this.transactionRepository.getTotalAmountByDateRange(dateFrom, dateTo)
  }

  async getTransactionSummaryByPurpose() {
    return await this.transactionRepository.getTransactionSummaryByPurpose()
  }
}