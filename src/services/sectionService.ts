import { z } from 'zod'
import crypto from 'crypto'
import type { SectionRepository } from '@/repositories/sectionRepository'

// Input validation schemas
const CreateSectionInput = z.object({
  stationId: z.string().uuid(),
  sectionName: z.string().min(1),
  sectionNumber: z.string().min(1),
})

const UpdateSectionInput = z.object({
  sectionName: z.string().min(1).optional(),
  sectionNumber: z.string().min(1).optional(),
})

const SectionFilters = z.object({
  syncStatus: z.string().optional(),
  stationId: z.string().uuid().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

export class SectionService {
  constructor(private sectionRepository: SectionRepository) {}

  async createSection(input: z.infer<typeof CreateSectionInput>) {
    const data = CreateSectionInput.parse(input)

    const now = new Date().toISOString()
    const id = crypto.randomUUID()

    return await this.sectionRepository.create({
      id,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
      stationId: data.stationId,
      sectionName: data.sectionName,
      sectionNumber: data.sectionNumber,
    })
  }

  async getSectionById(id: string) {
    return await this.sectionRepository.getById(id)
  }

  async updateSection(id: string, patch: z.infer<typeof UpdateSectionInput>) {
    const data = UpdateSectionInput.parse(patch)

    return await this.sectionRepository.update(id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })
  }

  async deleteSection(id: string) {
    const when = new Date().toISOString()
    return await this.sectionRepository.softDelete(id, when)
  }

  async listSections(filters: z.infer<typeof SectionFilters> = {}) {
    const validated = SectionFilters.parse(filters)
    return await this.sectionRepository.findMany(validated)
  }

  async getSectionsByStation(stationId: string) {
    return await this.sectionRepository.findByStation(stationId)
  }

  async searchSectionsByName(searchTerm: string) {
    return await this.sectionRepository.searchByName(searchTerm)
  }

  async getSectionWithStation(id: string) {
    return await this.sectionRepository.getWithStation(id)
  }

  async getSectionWithVoterCount(id: string) {
    return await this.sectionRepository.getWithVoterCount(id)
  }

  async findByNumber(stationId: string, sectionNumber: string) {
    return await this.sectionRepository.findByNumberInStation(
      stationId,
      sectionNumber
    )
  }
}
