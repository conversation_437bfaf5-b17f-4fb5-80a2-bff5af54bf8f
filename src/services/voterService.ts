import type { VoterRepository } from '@/repositories/voterRepository'
import {
  VoterSchemas,
  CreateVoterInput,
  UpdateVoterInput,
  VoterFilters,
  Voter,
} from '@/shared/schemas'
import { ErrorFactory } from '@/shared/errors'
import { CryptoService } from '@/shared/cryptoService'

export class VoterService {
  constructor(private voterRepository: VoterRepository) {}

  async createVoter(input: CreateVoterInput): Promise<Voter> {
    const data = VoterSchemas.create.parse(input)

    const existing = await this.voterRepository.findByEpicNumber(
      data.epicNumber
    )
    if (existing) {
      throw ErrorFactory.conflict('EPIC number already exists', {
        epicNumber: data.epicNumber,
        existingVoterId: existing.id,
      })
    }

    const now = new Date().toISOString()
    const id = CryptoService.generateId()

    return await this.voterRepository.create({
      id,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
      stationId: data.stationId,
      sectionId: data.sectionId,
      name: data.name,
      epicNumber: data.epicNumber,
      houseNumber: data.houseNumber,
      birthYear: data.birthYear,
      gender: data.gender,
      relationType: data.relationType,
      status: data.status,
      phone: data.phone,
      email: data.email,
      facebook: data.facebook,
      instagram: data.instagram,
      twitter: data.twitter,
      supporterStatus: data.supporterStatus,
      education: data.education,
      occupation: data.occupation,
      community: data.community,
      religion: data.religion,
      economicStatus: data.economicStatus,
      customNotes: data.customNotes,
      relationName: data.relationName,
    })
  }

  async updateVoter(id: string, patch: UpdateVoterInput): Promise<boolean> {
    const data = VoterSchemas.update.parse(patch)

    if (data.epicNumber) {
      const current = await this.voterRepository.getById(id)
      if (!current) {
        throw ErrorFactory.notFound('Voter', id)
      }
      if (current.epicNumber !== data.epicNumber) {
        const duplicate = await this.voterRepository.findByEpicNumber(
          data.epicNumber
        )
        if (duplicate && duplicate.id !== id) {
          throw ErrorFactory.conflict('EPIC number already exists', {
            epicNumber: data.epicNumber,
            existingVoterId: duplicate.id,
          })
        }
      }
    }

    return await this.voterRepository.update(id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })
  }

  async deleteVoter(id: string): Promise<boolean> {
    const when = new Date().toISOString()
    return await this.voterRepository.softDelete(id, when)
  }

  async listVoters(
    filters: VoterFilters = {}
  ): Promise<{ items: Voter[]; total: number }> {
    const validated = VoterSchemas.filters.parse(filters)
    const [items, total] = await Promise.all([
      this.voterRepository.findMany(validated),
      this.voterRepository.count(validated),
    ])
    return { items, total }
  }

  async getVoterById(id: string): Promise<Voter | null> {
    return await this.voterRepository.getById(id)
  }

  async search(
    term: string,
    limit = 50
  ): Promise<import('@/repositories/voterRepository').Voter[]> {
    return await this.voterRepository.searchByText(term, limit)
  }

  async bulkImport(
    inputs: z.infer<typeof CreateVoterInput>[],
    options?: { progress?: (processed: number, total: number) => void }
  ): Promise<{
    processed: number
    succeeded: number
    failed: number
    errors: { index: number; message: string }[]
  }> {
    const total = inputs.length
    let processed = 0
    let succeeded = 0
    const errors: { index: number; message: string }[] = []

    for (let i = 0; i < inputs.length; i++) {
      try {
        await this.createVoter(inputs[i])
        succeeded += 1
      } catch (e) {
        const message = e instanceof Error ? e.message : 'Unknown error'
        errors.push({ index: i, message })
      } finally {
        processed += 1
        options?.progress?.(processed, total)
      }
    }

    return { processed, succeeded, failed: processed - succeeded, errors }
  }
}
