import React, { Component, ErrorInfo, ReactNode } from 'react'
import { isDomainError, getErrorDetails } from '@/shared/errors'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

/**
 * Error boundary component for graceful error handling in React
 * Catches JavaScript errors anywhere in the child component tree
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    const details = getErrorDetails(error)
    console.error('ErrorBoundary caught an error:', {
      ...details,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name,
    })

    // Call optional error handler
    this.props.onError?.(error, errorInfo)

    // Update state with error info
    this.setState({ errorInfo })
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  private renderErrorDetails() {
    const { error } = this.state
    if (!error) return null

    const details = getErrorDetails(error)
    const isDomain = isDomainError(error)

    return (
      <div className="error-details">
        <h3>Error Details</h3>
        <div className="error-info">
          <p><strong>Code:</strong> {details.code}</p>
          <p><strong>Message:</strong> {details.message}</p>
          {isDomain && details.details && (
            <div>
              <strong>Additional Info:</strong>
              <pre>{JSON.stringify(details.details, null, 2)}</pre>
            </div>
          )}
        </div>
      </div>
    )
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="error-boundary">
          <div className="error-container">
            <h2>Something went wrong</h2>
            <p>An unexpected error occurred. Please try again or contact support if the problem persists.</p>
            
            <div className="error-actions">
              <button onClick={this.handleRetry} className="retry-button">
                Try Again
              </button>
              <button 
                onClick={() => window.location.reload()} 
                className="reload-button"
              >
                Reload Page
              </button>
            </div>

            {process.env.NODE_ENV === 'development' && this.renderErrorDetails()}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Hook-based error boundary for functional components
 */
export function useErrorHandler() {
  return (error: Error, errorInfo?: { componentStack?: string }) => {
    const details = getErrorDetails(error)
    console.error('Error caught by error handler:', {
      ...details,
      componentStack: errorInfo?.componentStack,
    })

    // In a real app, you might want to send this to an error reporting service
    // Example: Sentry.captureException(error, { extra: details })
  }
}

/**
 * Higher-order component for adding error boundary to any component
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Async error boundary for handling promise rejections
 */
export class AsyncErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const details = getErrorDetails(error)
    console.error('AsyncErrorBoundary caught an error:', {
      ...details,
      componentStack: errorInfo.componentStack,
    })

    this.props.onError?.(error, errorInfo)
    this.setState({ errorInfo })
  }

  componentDidMount() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection)
  }

  componentWillUnmount() {
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection)
  }

  private handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason))
    this.setState({ hasError: true, error })
    event.preventDefault()
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="async-error-boundary">
          <h2>An error occurred</h2>
          <p>Please try again or refresh the page.</p>
          <button onClick={this.handleRetry}>Try Again</button>
        </div>
      )
    }

    return this.props.children
  }
}
