import styles from './TableToolbar.module.css'

interface TableToolbarProps {
  searchValue?: string
  onSearchChange?: (value: string) => void
  onColumnsToggle?: () => void
  onExport?: () => void
  onInfo?: () => void
  totalCount?: number
  filteredCount?: number
}

export function TableToolbar({
  searchValue = '',
  onSearchChange,
  onColumnsToggle,
  onExport,
  onInfo,
  totalCount = 0,
  filteredCount = 0
}: TableToolbarProps) {
  return (
    <div className={styles.toolbar}>
      <div className={styles.left}>
        <input
          type="text"
          placeholder="Search..."
          value={searchValue}
          onChange={(e) => onSearchChange?.(e.target.value)}
          className={styles.searchInput}
        />
      </div>

      <div className={styles.center}>
        <span className={styles.count}>
          {filteredCount !== totalCount
            ? `${filteredCount} of ${totalCount} records`
            : `${totalCount} records`
          }
        </span>
      </div>

      <div className={styles.right}>
        <button className={styles.button} onClick={onColumnsToggle}>
          Columns
        </button>
        <button className={styles.button} onClick={onExport}>
          Export
        </button>
        <button className={styles.button} onClick={onInfo}>
          Info
        </button>
      </div>
    </div>
  )
}