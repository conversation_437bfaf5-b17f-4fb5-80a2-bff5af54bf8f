import styles from './StatusBar.module.css'

interface StatusBarProps {
  selectedCount?: number
  totalCount?: number
  status?: string
}

export function StatusBar({ selectedCount = 0, totalCount = 0, status }: StatusBarProps) {
  return (
    <div className={styles.statusBar}>
      <div className={styles.info}>
        {selectedCount > 0 && (
          <span className={styles.selection}>
            {selectedCount} of {totalCount} selected
          </span>
        )}
        {status && <span className={styles.status}>{status}</span>}
      </div>
    </div>
  )
}