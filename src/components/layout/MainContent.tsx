import { useState } from 'react'
import { Toolbar } from './Toolbar'
import { StatCards } from './StatCards'
import { TableToolbar } from './TableToolbar'
import { MainTable } from './MainTable'
import { FilterPanel } from './FilterPanel'
import { SystemConfig } from './SystemConfig'
import styles from './MainContent.module.css'

interface MainContentProps {
  currentView?: 'voters' | 'reports' | 'config'
  onViewChange?: (view: string) => void
}

export function MainContent({
  currentView = 'voters',
  onViewChange,
}: MainContentProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())

  // Mock data - will be replaced with real data
  const mockStatCards = [
    { id: '1', title: 'Total Voters', value: '12,345', subtitle: 'Registered' },
    { id: '2', title: 'Active Voters', value: '11,890', subtitle: 'This year' },
    {
      id: '3',
      title: 'Turnout Rate',
      value: '78.5%',
      subtitle: 'Last election',
    },
    {
      id: '4',
      title: 'New Registrations',
      value: '234',
      subtitle: 'This month',
    },
  ]

  const mockColumns = [
    { id: 'name', header: 'Name', accessorKey: 'name' },
    { id: 'epicNumber', header: 'EPIC Number', accessorKey: 'epicNumber' },
    { id: 'section', header: 'Section', accessorKey: 'section' },
    { id: 'station', header: 'Polling Station', accessorKey: 'station' },
  ]

  const mockData = [
    {
      id: '1',
      name: 'John Doe',
      epicNumber: 'ABC123456',
      section: 'Section 1',
      station: 'Station A',
    },
    {
      id: '2',
      name: 'Jane Smith',
      epicNumber: 'DEF789012',
      section: 'Section 2',
      station: 'Station B',
    },
  ]

  const handleRowSelect = (rowId: string) => {
    const newSelected = new Set(selectedRows)
    if (newSelected.has(rowId)) {
      newSelected.delete(rowId)
    } else {
      newSelected.add(rowId)
    }
    setSelectedRows(newSelected)
  }

  const renderContent = () => {
    switch (currentView) {
      case 'config':
        return <SystemConfig />

      case 'reports':
        return (
          <div className={styles.reportsView}>
            <h2>Reports Section</h2>
            <p>Reports functionality will be implemented here</p>
          </div>
        )

      case 'voters':
      default:
        return (
          <>
            {/* Row 1: Toolbar */}
            <div className={styles.row}>
              <Toolbar
                title="Voters"
                onFilterToggle={() => setIsFilterOpen(!isFilterOpen)}
                onReportsToggle={() => onViewChange?.('reports')}
                onAddVoter={() => console.log('Add voter clicked')}
              />
            </div>

            {/* Row 2: Stat Cards */}
            <div className={styles.row}>
              <StatCards cards={mockStatCards} />
            </div>

            {/* Row 3: Table Toolbar */}
            <div className={styles.row}>
              <TableToolbar
                searchValue={searchValue}
                onSearchChange={setSearchValue}
                onColumnsToggle={() => console.log('Columns clicked')}
                onExport={() => console.log('Export clicked')}
                onInfo={() => console.log('Info clicked')}
                totalCount={mockData.length}
                filteredCount={mockData.length}
              />
            </div>

            {/* Row 4: Main Table */}
            <div className={styles.row}>
              <MainTable
                data={mockData}
                columns={mockColumns}
                selectedRows={selectedRows}
                onRowSelect={handleRowSelect}
                onRowClick={(row) => console.log('Row clicked:', row)}
                pagination={{
                  pageIndex: 0,
                  pageSize: 50,
                  pageCount: 1,
                  onPageChange: (page) => console.log('Page changed:', page),
                  onPageSizeChange: (size) =>
                    console.log('Page size changed:', size),
                }}
              />
            </div>
          </>
        )
    }
  }

  return (
    <div className={styles.container}>
      {renderContent()}

      {/* Filter Panel */}
      <FilterPanel
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        filters={[
          {
            id: 'section',
            label: 'Section',
            type: 'select',
            options: [
              { value: 'section1', label: 'Section 1' },
              { value: 'section2', label: 'Section 2' },
            ],
          },
          {
            id: 'station',
            label: 'Polling Station',
            type: 'select',
            options: [
              { value: 'stationA', label: 'Station A' },
              { value: 'stationB', label: 'Station B' },
            ],
          },
          { id: 'name', label: 'Name', type: 'text' },
        ]}
        onFiltersChange={(filters) => console.log('Filters changed:', filters)}
        onReset={() => console.log('Filters reset')}
        onApply={() => setIsFilterOpen(false)}
      />
    </div>
  )
}
