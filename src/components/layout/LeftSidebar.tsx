import { useState, useEffect } from 'react'
import { Box, Flex, Text, Checkbox, IconButton } from '@radix-ui/themes'
import * as Collapsible from '@radix-ui/react-collapsible'
import { ChevronRightIcon } from '@radix-ui/react-icons'
import styles from './LeftSidebar.module.css'

// Types for our data structure
interface Station {
  id: string
  stationName: string
  stationNumber: string
}

interface Section {
  id: string
  stationId: string
  sectionName: string
  sectionNumber: string
}

interface TreeState {
  expandedStations: Set<string>
  selectedStations: Set<string>
  selectedSections: Set<string>
}

interface LeftSidebarProps {
  stations?: Station[]
  sections?: Section[]
  onSelectionChange?: (
    selectedStations: string[],
    selectedSections: string[]
  ) => void
}

export function LeftSidebar({
  stations = [],
  sections = [],
  onSelectionChange,
}: LeftSidebarProps) {
  const [treeState, setTreeState] = useState<TreeState>({
    expandedStations: new Set(),
    selectedStations: new Set(),
    selectedSections: new Set(),
  })

  // Group sections by station (with safety check)
  const sectionsByStation = (sections || []).reduce(
    (acc, section) => {
      if (!acc[section.stationId]) {
        acc[section.stationId] = []
      }
      acc[section.stationId].push(section)
      return acc
    },
    {} as Record<string, Section[]>
  )

  // Toggle station expansion
  const toggleStationExpansion = (stationId: string) => {
    setTreeState((prev) => {
      const newExpanded = new Set(prev.expandedStations)
      if (newExpanded.has(stationId)) {
        newExpanded.delete(stationId)
      } else {
        newExpanded.add(stationId)
      }
      return { ...prev, expandedStations: newExpanded }
    })
  }

  // Handle station checkbox change
  const handleStationCheckboxChange = (stationId: string, checked: boolean) => {
    setTreeState((prev) => {
      const newSelectedStations = new Set(prev.selectedStations)
      const newSelectedSections = new Set(prev.selectedSections)

      const stationSections = sectionsByStation[stationId] || []

      if (checked) {
        newSelectedStations.add(stationId)
        // Select all sections in this station
        stationSections.forEach((section) => {
          newSelectedSections.add(section.id)
        })
      } else {
        newSelectedStations.delete(stationId)
        // Deselect all sections in this station
        stationSections.forEach((section) => {
          newSelectedSections.delete(section.id)
        })
      }

      return {
        ...prev,
        selectedStations: newSelectedStations,
        selectedSections: newSelectedSections,
      }
    })
  }

  // Handle section checkbox change
  const handleSectionCheckboxChange = (
    sectionId: string,
    stationId: string,
    checked: boolean
  ) => {
    setTreeState((prev) => {
      const newSelectedSections = new Set(prev.selectedSections)
      const newSelectedStations = new Set(prev.selectedStations)

      if (checked) {
        newSelectedSections.add(sectionId)
      } else {
        newSelectedSections.delete(sectionId)
      }

      // Check if all sections in this station are selected
      const stationSections = sectionsByStation[stationId] || []
      const allSectionsSelected = stationSections.every((section) =>
        newSelectedSections.has(section.id)
      )
      if (allSectionsSelected) {
        newSelectedStations.add(stationId)
      } else {
        newSelectedStations.delete(stationId)
      }

      return {
        ...prev,
        selectedStations: newSelectedStations,
        selectedSections: newSelectedSections,
      }
    })
  }

  // Get checkbox state for a station (checked, unchecked, or indeterminate)
  const getStationCheckboxState = (stationId: string) => {
    const stationSections = sectionsByStation[stationId] || []
    const selectedSectionsInStation = stationSections.filter((section) =>
      treeState.selectedSections.has(section.id)
    )

    if (selectedSectionsInStation.length === 0) {
      return false // unchecked
    } else if (selectedSectionsInStation.length === stationSections.length) {
      return true // checked
    } else {
      return 'indeterminate' // indeterminate
    }
  }

  // Notify parent component of selection changes
  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(
        Array.from(treeState.selectedStations),
        Array.from(treeState.selectedSections)
      )
    }
  }, [
    treeState.selectedStations,
    treeState.selectedSections,
    onSelectionChange,
  ])

  return (
    <Box
      p="4"
      style={{ width: '300px', borderRight: '1px solid var(--gray-6)' }}
    >
      <Text size="4" weight="bold" mb="4">
        Stations & Sections
      </Text>

      <Flex direction="column" gap="2">
        {(stations || []).map((station) => {
          const isExpanded = treeState.expandedStations.has(station.id)
          const checkboxState = getStationCheckboxState(station.id)
          const stationSections = sectionsByStation[station.id] || []

          return (
            <Box key={station.id}>
              {/* Station Header */}
              <Collapsible.Root
                open={isExpanded}
                onOpenChange={() => toggleStationExpansion(station.id)}
              >
                <Flex align="center" gap="2" className={styles.stationHeader}>
                  {/* Expand/Collapse Button */}
                  <Collapsible.Trigger asChild>
                    <IconButton
                      variant="ghost"
                      size="1"
                      style={{ minWidth: '20px', minHeight: '20px' }}
                    >
                      <ChevronRightIcon
                        width="14"
                        height="14"
                        className={styles.chevronIcon}
                        data-state={isExpanded ? 'open' : 'closed'}
                      />
                    </IconButton>
                  </Collapsible.Trigger>

                  {/* Station Checkbox */}
                  <Checkbox
                    checked={checkboxState}
                    onCheckedChange={(checked) =>
                      handleStationCheckboxChange(station.id, checked === true)
                    }
                  />

                  {/* Station Name */}
                  <Text size="3" weight="medium">
                    {station.stationNumber} - {station.stationName}
                  </Text>
                </Flex>

                {/* Sections (Collapsible Content) */}
                <Collapsible.Content className={styles.collapsibleContent}>
                  <Box className={styles.sectionList}>
                    <Flex direction="column" gap="1">
                      {stationSections.map((section) => (
                        <Flex
                          key={section.id}
                          align="center"
                          gap="2"
                          className={styles.sectionItem}
                        >
                          {/* Section Checkbox */}
                          <Checkbox
                            checked={treeState.selectedSections.has(section.id)}
                            onCheckedChange={(checked) =>
                              handleSectionCheckboxChange(
                                section.id,
                                station.id,
                                checked === true
                              )
                            }
                          />

                          {/* Section Name */}
                          <Text size="2">
                            {section.sectionNumber} - {section.sectionName}
                          </Text>
                        </Flex>
                      ))}
                    </Flex>
                  </Box>
                </Collapsible.Content>
              </Collapsible.Root>
            </Box>
          )
        })}
      </Flex>
    </Box>
  )
}
