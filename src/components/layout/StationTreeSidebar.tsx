import React from 'react'
import { LeftSidebar } from './LeftSidebar'
import { useStationsAndSections } from '../../hooks/useStationsAndSections'
import { Box, Text } from '@radix-ui/themes'

interface StationTreeSidebarProps {
  onSelectionChange?: (
    selectedStations: string[],
    selectedSections: string[]
  ) => void
}

export function StationTreeSidebar({
  onSelectionChange,
}: StationTreeSidebarProps) {
  const { stations, sections, loading, error } = useStationsAndSections()

  if (loading) {
    return (
      <Box
        p="4"
        style={{ width: '300px', borderRight: '1px solid var(--gray-6)' }}
      >
        <Text size="2" color="gray">
          Loading stations...
        </Text>
      </Box>
    )
  }

  if (error) {
    return (
      <Box
        p="4"
        style={{ width: '300px', borderRight: '1px solid var(--gray-6)' }}
      >
        <Text size="2" color="red">
          Error: {error}
        </Text>
      </Box>
    )
  }

  return (
    <LeftSidebar
      stations={stations}
      sections={sections}
      onSelectionChange={onSelectionChange}
    />
  )
}
