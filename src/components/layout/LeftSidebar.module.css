/* Collapsible content animations */
.collapsibleContent {
  overflow: hidden;
}

.collapsibleContent[data-state='open'] {
  animation: slideDown 200ms ease-out;
}

.collapsibleContent[data-state='closed'] {
  animation: slideUp 200ms ease-out;
}

/* Chevron icon rotation */
.chevronIcon {
  transition: transform 200ms ease;
}

.chevronIcon[data-state='open'] {
  transform: rotate(90deg);
}

/* Tree indentation */
.stationHeader {
  padding: 4px 0;
}

.sectionList {
  padding-top: 4px;
  padding-left: 24px;
}

.sectionItem {
  padding: 2px 0;
}

/* Animations */
@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}
