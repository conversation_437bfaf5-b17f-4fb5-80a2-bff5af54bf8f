import styles from './StatCards.module.css'

interface StatCard {
  id: string
  title: string
  value: string | number
  subtitle?: string
  trend?: 'up' | 'down' | 'neutral'
  trendValue?: string
}

interface StatCardsProps {
  cards?: StatCard[]
}

export function StatCards({ cards = [] }: StatCardsProps) {
  return (
    <div className={styles.container}>
      {cards.map((card) => (
        <div key={card.id} className={styles.card}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>{card.title}</h3>
          </div>
          <div className={styles.cardContent}>
            <div className={styles.value}>{card.value}</div>
            {card.subtitle && (
              <div className={styles.subtitle}>{card.subtitle}</div>
            )}
            {card.trend && card.trendValue && (
              <div className={`${styles.trend} ${styles[card.trend]}`}>
                {card.trendValue}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}