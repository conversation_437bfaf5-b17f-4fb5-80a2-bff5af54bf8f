import styles from './Toolbar.module.css'

interface ToolbarProps {
  title?: string
  onAddVoter?: () => void
  onFilterToggle?: () => void
  onReportsToggle?: () => void
  userAvatar?: string
  userName?: string
}

export function Toolbar({
  title = 'Voters',
  onAddVoter,
  onFilterToggle,
  onReportsToggle,
  userAvatar,
  userName = 'Admin'
}: ToolbarProps) {
  return (
    <div className={styles.toolbar}>
      <div className={styles.left}>
        <h1 className={styles.title}>{title}</h1>
      </div>

      <div className={styles.center}>
        <button className={styles.button} onClick={onFilterToggle}>
          Filter
        </button>
        <button className={styles.button} onClick={onReportsToggle}>
          Reports
        </button>
      </div>

      <div className={styles.right}>
        <button className={styles.addButton} onClick={onAddVoter}>
          Add Voter
        </button>
        <div className={styles.userMenu}>
          <button className={styles.avatar}>
            {userAvatar ? (
              <img src={userAvatar} alt={userName} className={styles.avatarImage} />
            ) : (
              <span className={styles.avatarText}>{userName.charAt(0)}</span>
            )}
          </button>
          {/* Dropdown menu will be implemented here */}
        </div>
      </div>
    </div>
  )
}