import styles from './CustomTitleBar.module.css'

interface CustomTitleBarProps {
  title?: string
}

export function CustomTitleBar({ title = 'Electixir' }: CustomTitleBarProps) {
  return (
    <div className={styles.titleBar}>
      <div className={styles.title}>{title}</div>
      <div className={styles.controls}>
        {/* Window controls will be implemented here */}
        <button className={styles.controlButton}>−</button>
        <button className={styles.controlButton}>□</button>
        <button className={styles.controlButton}>×</button>
      </div>
    </div>
  )
}
