import { QueryClientProvider } from '@tanstack/react-query'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'
import { LoginPage } from '@/pages/LoginPage'
import { MainPage } from '@/pages/MainPage'
import { useAuth } from '@/hooks/useAuth'
import { queryClient } from '@/utils/queryClient'
import { setupGlobalErrorHandling } from '@/shared/errorLogger'
import { useEffect } from 'react'

function App() {
  const { user, isAuthenticated, isLoading, error, login, logout } = useAuth()

  // Set up global error handling on app initialization
  useEffect(() => {
    setupGlobalErrorHandling()
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>
        <>
          {isAuthenticated ? (
            <MainPage user={user} onLogout={logout} />
          ) : (
            <LoginPage onLogin={login} loading={isLoading} error={error} />
          )}
        </>
      </ErrorBoundary>
    </QueryClientProvider>
  )
}

export default App
