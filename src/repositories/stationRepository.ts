import type { InferSelectModel } from 'drizzle-orm'
import { eq, and, sql, like } from 'drizzle-orm'
import { stations, sections, voters } from '@/db/schema'
import type { drizzle } from 'drizzle-orm/better-sqlite3'
import { lru as createLRU } from 'tiny-lru'
import { z } from 'zod'

export type DB = ReturnType<typeof drizzle>

const CreateStationInput = z.object({
  id: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  syncStatus: z.string().min(1),
  stationName: z.string().min(1),
  stationNumber: z.string().min(1),
})

const UpdateStationInput = z.object({
  stationName: z.string().min(1).optional(),
  stationNumber: z.string().min(1).optional(),
  syncStatus: z.string().min(1).optional(),
  updatedAt: z.string(),
})

const StationFilters = z.object({
  syncStatus: z.string().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

type StationRow = InferSelectModel<typeof stations>
type Station = Omit<StationRow, 'deletedAt' | 'lastSyncAt'>
type SectionRow = InferSelectModel<typeof sections>
type Section = Omit<SectionRow, 'deletedAt' | 'lastSyncAt'>

export class StationRepository {
  private cache = createLRU<Station | null>(500)

  constructor(private db: DB) {}

  async create(input: z.infer<typeof CreateStationInput>): Promise<Station> {
    const data = CreateStationInput.parse(input)

    const row: Omit<StationRow, 'lastSyncAt'> & { lastSyncAt?: string | null } =
      {
        id: data.id,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        deletedAt: null,
        syncStatus: data.syncStatus,
        lastSyncAt: null,
        stationName: data.stationName,
        stationNumber: data.stationNumber,
      }

    await this.db.insert(stations).values(row).run()
    this.invalidateCache()

    return this.mapToStation(row as StationRow)
  }

  async getById(id: string): Promise<Station | null> {
    const cacheKey = `station:${id}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(stations)
      .where(and(eq(stations.id, id), sql`${stations.deletedAt} IS NULL`))

    const station = rows.at(0)
    if (!station) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToStation(station)
    this.cache.set(cacheKey, result)
    return result
  }

  async findByNumber(stationNumber: string): Promise<Station | null> {
    const cacheKey = `number:${stationNumber}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(stations)
      .where(
        and(
          eq(stations.stationNumber, stationNumber),
          sql`${stations.deletedAt} IS NULL`
        )
      )

    const station = rows.at(0)
    if (!station) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToStation(station)
    this.cache.set(cacheKey, result)
    return result
  }

  async update(
    id: string,
    patch: z.infer<typeof UpdateStationInput>
  ): Promise<boolean> {
    const data = UpdateStationInput.parse(patch)

    const updateData: Record<string, unknown> = {
      updatedAt: data.updatedAt,
    }

    // Only include fields that are provided
    Object.entries(data).forEach(([key, value]) => {
      if (key !== 'updatedAt' && value !== undefined) {
        updateData[key] = value
      }
    })

    const result = await this.db
      .update(stations)
      .set(updateData)
      .where(and(eq(stations.id, id), sql`${stations.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async softDelete(id: string, deletedAt: string): Promise<boolean> {
    // Check if there are active sections
    const activeSections = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(sections)
      .where(
        and(eq(sections.stationId, id), sql`${sections.deletedAt} IS NULL`)
      )

    const sectionCount = activeSections[0]?.count || 0
    if (sectionCount > 0) {
      throw new Error(
        `Cannot delete station: ${sectionCount} active sections exist`
      )
    }

    const result = await this.db
      .update(stations)
      .set({ deletedAt, updatedAt: deletedAt })
      .where(and(eq(stations.id, id), sql`${stations.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async cascadeDelete(id: string, deletedAt: string): Promise<boolean> {
    // First, soft delete all sections
    await this.db
      .update(sections)
      .set({ deletedAt, updatedAt: deletedAt })
      .where(
        and(eq(sections.stationId, id), sql`${sections.deletedAt} IS NULL`)
      )
      .run()

    // Then, soft delete the station
    const result = await this.db
      .update(stations)
      .set({ deletedAt, updatedAt: deletedAt })
      .where(and(eq(stations.id, id), sql`${stations.deletedAt} IS NULL`))
      .run()

    this.invalidateCache()
    return result.changes > 0
  }

  async findMany(
    filters: z.infer<typeof StationFilters> = {}
  ): Promise<Station[]> {
    const validatedFilters = StationFilters.parse(filters)

    const conditions = [sql`${stations.deletedAt} IS NULL`]

    if (validatedFilters.syncStatus) {
      conditions.push(eq(stations.syncStatus, validatedFilters.syncStatus))
    }

    let query = this.db
      .select()
      .from(stations)
      .where(and(...conditions))

    // Apply pagination
    if (validatedFilters.limit) {
      query = query.limit(validatedFilters.limit)
    }

    if (validatedFilters.offset) {
      query = query.offset(validatedFilters.offset)
    }

    const rows = await query.all()
    return rows.map((row) => this.mapToStation(row))
  }

  async searchByName(searchTerm: string): Promise<Station[]> {
    if (!searchTerm.trim()) {
      return []
    }

    const rows = await this.db
      .select()
      .from(stations)
      .where(
        and(
          like(stations.stationName, `%${searchTerm}%`),
          sql`${stations.deletedAt} IS NULL`
        )
      )

    return rows.map((row) => this.mapToStation(row))
  }

  async getWithSections(
    id: string
  ): Promise<{ station: Station; sections: Section[] } | null> {
    const station = await this.getById(id)
    if (!station) {
      return null
    }

    const sectionRows = await this.db
      .select()
      .from(sections)
      .where(
        and(eq(sections.stationId, id), sql`${sections.deletedAt} IS NULL`)
      )

    const sectionsData = sectionRows.map((row) => this.mapToSection(row))

    return {
      station,
      sections: sectionsData,
    }
  }

  async getWithVoterCount(
    id: string
  ): Promise<{ station: Station; voterCount: number } | null> {
    const station = await this.getById(id)
    if (!station) {
      return null
    }

    const voterCountResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(voters)
      .where(and(eq(voters.stationId, id), sql`${voters.deletedAt} IS NULL`))

    const voterCount = voterCountResult[0]?.count || 0

    return {
      station,
      voterCount,
    }
  }

  private mapToStation(row: StationRow): Station {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { deletedAt, lastSyncAt, ...station } = row
    return station
  }

  private mapToSection(row: SectionRow): Section {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { deletedAt, lastSyncAt, ...section } = row
    return section
  }

  private invalidateCache(): void {
    this.cache.clear()
  }
}
