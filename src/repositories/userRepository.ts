import type { InferSelectModel } from 'drizzle-orm'
import { eq } from 'drizzle-orm'
import { users } from '@/db/schema'
import type { drizzle } from 'drizzle-orm/better-sqlite3'
import { lru as createLRU } from 'tiny-lru'
import { z } from 'zod'
import { CryptoService } from '@/shared/cryptoService'

export type DB = ReturnType<typeof drizzle>

const CreateUserInput = z.object({
  id: z.string().uuid(),
  username: z.string().min(3),
  password: z.string().min(6),
  role: z.string().min(1),
  isActive: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
  syncStatus: z.string().min(1),
})

const UpdateUserInput = z.object({
  role: z.string().min(1).optional(),
  isActive: z.boolean().optional(),
  updatedAt: z.string(),
})

type UserRow = InferSelectModel<typeof users>

export class UserRepository {
  private cache = createLRU<UserRow | null>(100)
  constructor(private db: DB) {}

  async create(input: z.infer<typeof CreateUserInput>) {
    const data = CreateUserInput.parse(input)
    // Use shared crypto service for consistent password hashing
    const hash = await CryptoService.hashPassword(data.password)
    const row: Omit<UserRow, 'lastLoginAt'> & { lastLoginAt?: string | null } =
      {
        id: data.id,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        deletedAt: null,
        syncStatus: data.syncStatus,
        lastSyncAt: null,
        username: data.username,
        passwordHash: hash,
        role: data.role,
        isActive: data.isActive,
        lastLoginAt: null,
      }

    await this.db.insert(users).values(row).run()
    this.cache.delete(this.keyByUsername(data.username))
    return {
      id: row.id,
      username: row.username,
      role: row.role,
      isActive: row.isActive,
    }
  }

  async getById(id: string) {
    const rows = await this.db.select().from(users).where(eq(users.id, id))
    const u = rows.at(0)
    if (!u || u.deletedAt) return null
    return {
      id: u.id,
      username: u.username,
      role: u.role,
      isActive: u.isActive,
    }
  }

  private keyByUsername(username: string) {
    return `user:${username}`
  }

  async getByUsername(username: string) {
    const k = this.keyByUsername(username)
    const cached = this.cache.get(k)
    if (cached !== undefined) {
      const u = cached
      return u && !u.deletedAt
        ? { id: u.id, username: u.username, role: u.role, isActive: u.isActive }
        : null
    }
    const rows = await this.db
      .select()
      .from(users)
      .where(eq(users.username, username))
    const u = rows.at(0) ?? null
    this.cache.set(k, u)
    if (!u || u.deletedAt) return null
    return {
      id: u.id,
      username: u.username,
      role: u.role,
      isActive: u.isActive,
    }
  }

  async update(id: string, patch: z.infer<typeof UpdateUserInput>) {
    const data = UpdateUserInput.parse(patch)
    const rows = await this.db
      .update(users)
      .set({
        role: data.role,
        isActive: data.isActive,
        updatedAt: data.updatedAt,
      })
      .where(eq(users.id, id))
      .run()
    // crude invalidation: clear all; small cache anyway
    this.cache.clear()
    return rows.changes > 0
  }

  async softDelete(id: string, deletedAt: string) {
    const res = await this.db
      .update(users)
      .set({ deletedAt })
      .where(eq(users.id, id))
      .run()
    this.cache.clear()
    return res.changes > 0
  }

  async authenticate(username: string, password: string) {
    const rows = await this.db
      .select()
      .from(users)
      .where(eq(users.username, username))
    const u = rows.at(0)
    if (!u || u.deletedAt || !u.isActive) return null
    const ok = await CryptoService.verifyPassword(password, u.passwordHash)
    if (!ok) return null
    const ts = new Date().toISOString()
    await this.db
      .update(users)
      .set({ lastLoginAt: ts })
      .where(eq(users.id, u.id))
      .run()
    this.cache.set(this.keyByUsername(username), { ...u, lastLoginAt: ts })
    return {
      id: u.id,
      username: u.username,
      role: u.role,
      isActive: u.isActive,
    }
  }
}
