import type { InferSelectModel } from 'drizzle-orm'
import { eq, and, sql, like, gte, lte } from 'drizzle-orm'
import { transactions } from '@/db/schema'
import type { drizzle } from 'drizzle-orm/better-sqlite3'
import { lru as createLRU } from 'tiny-lru'
import { z } from 'zod'

export type DB = ReturnType<typeof drizzle>

const CreateTransactionInput = z.object({
  id: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  syncStatus: z.string().min(1),
  voterId: z.string().uuid(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
  purpose: z.string().min(1),
  amount: z.string().regex(/^\d+\.\d{2}$/), // Decimal string format
})

const UpdateTransactionInput = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  purpose: z.string().min(1).optional(),
  amount: z.string().regex(/^\d+\.\d{2}$/).optional(),
  syncStatus: z.string().min(1).optional(),
  updatedAt: z.string(),
})

const TransactionFilters = z.object({
  syncStatus: z.string().optional(),
  voterId: z.string().uuid().optional(),
  dateFrom: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  dateTo: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  purpose: z.string().optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

type TransactionRow = InferSelectModel<typeof transactions>
type Transaction = Omit<TransactionRow, 'deletedAt' | 'lastSyncAt'>

export class TransactionRepository {
  private cache = createLRU<Transaction | null>(1000)

  constructor(private db: DB) {}

  async create(input: z.infer<typeof CreateTransactionInput>): Promise<Transaction> {
    const data = CreateTransactionInput.parse(input)

    const row: Omit<TransactionRow, 'lastSyncAt'> & { lastSyncAt?: string | null } = {
      id: data.id,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: null,
      syncStatus: data.syncStatus,
      lastSyncAt: null,
      voterId: data.voterId,
      date: data.date,
      purpose: data.purpose,
      amount: data.amount,
    }

    await this.db.insert(transactions).values(row).run()
    this.invalidateCache()

    return this.mapToTransaction(row as TransactionRow)
  }

  async getById(id: string): Promise<Transaction | null> {
    const cacheKey = `transaction:${id}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(transactions)
      .where(and(eq(transactions.id, id), sql`${transactions.deletedAt} IS NULL`))

    const transaction = rows.at(0)
    if (!transaction) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToTransaction(transaction)
    this.cache.set(cacheKey, result)
    return result
  }

  async update(id: string, patch: z.infer<typeof UpdateTransactionInput>): Promise<boolean> {
    const data = UpdateTransactionInput.parse(patch)

    const updateData: Record<string, unknown> = {
      updatedAt: data.updatedAt,
    }

    // Only include fields that are provided
    Object.entries(data).forEach(([key, value]) => {
      if (key !== 'updatedAt' && value !== undefined) {
        updateData[key] = value
      }
    })

    const result = await this.db
      .update(transactions)
      .set(updateData)
      .where(and(eq(transactions.id, id), sql`${transactions.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async softDelete(id: string, deletedAt: string): Promise<boolean> {
    const result = await this.db
      .update(transactions)
      .set({ deletedAt, updatedAt: deletedAt })
      .where(and(eq(transactions.id, id), sql`${transactions.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async findMany(filters: z.infer<typeof TransactionFilters> = {}): Promise<Transaction[]> {
    const validatedFilters = TransactionFilters.parse(filters)

    const conditions = [sql`${transactions.deletedAt} IS NULL`]

    if (validatedFilters.syncStatus) {
      conditions.push(eq(transactions.syncStatus, validatedFilters.syncStatus))
    }

    if (validatedFilters.voterId) {
      conditions.push(eq(transactions.voterId, validatedFilters.voterId))
    }

    if (validatedFilters.dateFrom) {
      conditions.push(gte(transactions.date, validatedFilters.dateFrom))
    }

    if (validatedFilters.dateTo) {
      conditions.push(lte(transactions.date, validatedFilters.dateTo))
    }

    if (validatedFilters.purpose) {
      conditions.push(like(transactions.purpose, `%${validatedFilters.purpose}%`))
    }

    if (validatedFilters.minAmount !== undefined) {
      conditions.push(sql`CAST(${transactions.amount} AS REAL) >= ${validatedFilters.minAmount}`)
    }

    if (validatedFilters.maxAmount !== undefined) {
      conditions.push(sql`CAST(${transactions.amount} AS REAL) <= ${validatedFilters.maxAmount}`)
    }

    let query = this.db
      .select()
      .from(transactions)
      .where(and(...conditions))
      .orderBy(sql`${transactions.date} DESC, ${transactions.createdAt} DESC`)

    // Apply pagination
    if (validatedFilters.limit) {
      query = query.limit(validatedFilters.limit)
    }

    if (validatedFilters.offset) {
      query = query.offset(validatedFilters.offset)
    }

    const rows = await query.all()
    return rows.map(row => this.mapToTransaction(row))
  }

  async findByVoter(voterId: string): Promise<Transaction[]> {
    const cacheKey = `voter-transactions:${voterId}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached as Transaction[]
    }

    const rows = await this.db
      .select()
      .from(transactions)
      .where(and(
        eq(transactions.voterId, voterId),
        sql`${transactions.deletedAt} IS NULL`
      ))
      .orderBy(sql`${transactions.date} DESC`)

    const results = rows.map(row => this.mapToTransaction(row))
    this.cache.set(cacheKey, results)
    return results
  }

  async findByDateRange(dateFrom: string, dateTo: string): Promise<Transaction[]> {
    const rows = await this.db
      .select()
      .from(transactions)
      .where(and(
        gte(transactions.date, dateFrom),
        lte(transactions.date, dateTo),
        sql`${transactions.deletedAt} IS NULL`
      ))
      .orderBy(sql`${transactions.date} DESC`)

    return rows.map(row => this.mapToTransaction(row))
  }

  async findByPurpose(purposeSearch: string): Promise<Transaction[]> {
    const rows = await this.db
      .select()
      .from(transactions)
      .where(and(
        like(transactions.purpose, `%${purposeSearch}%`),
        sql`${transactions.deletedAt} IS NULL`
      ))
      .orderBy(sql`${transactions.date} DESC`)

    return rows.map(row => this.mapToTransaction(row))
  }

  async getTransactionHistory(voterId: string): Promise<Transaction[]> {
    return this.findByVoter(voterId)
  }

  async getTotalAmountByVoter(voterId: string): Promise<number> {
    const result = await this.db
      .select({
        total: sql<number>`SUM(CAST(${transactions.amount} AS REAL))`
      })
      .from(transactions)
      .where(and(
        eq(transactions.voterId, voterId),
        sql`${transactions.deletedAt} IS NULL`
      ))

    return result[0]?.total || 0
  }

  async getTotalAmountByPurpose(purposeSearch: string): Promise<number> {
    const result = await this.db
      .select({
        total: sql<number>`SUM(CAST(${transactions.amount} AS REAL))`
      })
      .from(transactions)
      .where(and(
        like(transactions.purpose, `%${purposeSearch}%`),
        sql`${transactions.deletedAt} IS NULL`
      ))

    return result[0]?.total || 0
  }

  async getTotalAmountByDateRange(dateFrom: string, dateTo: string): Promise<number> {
    const result = await this.db
      .select({
        total: sql<number>`SUM(CAST(${transactions.amount} AS REAL))`
      })
      .from(transactions)
      .where(and(
        gte(transactions.date, dateFrom),
        lte(transactions.date, dateTo),
        sql`${transactions.deletedAt} IS NULL`
      ))

    return result[0]?.total || 0
  }

  async getTransactionSummaryByPurpose(): Promise<Array<{ purpose: string; count: number; totalAmount: number }>> {
    const results = await this.db
      .select({
        purpose: transactions.purpose,
        count: sql<number>`COUNT(*)`,
        totalAmount: sql<number>`SUM(CAST(${transactions.amount} AS REAL))`
      })
      .from(transactions)
      .where(sql`${transactions.deletedAt} IS NULL`)
      .groupBy(transactions.purpose)
      .orderBy(sql`SUM(CAST(${transactions.amount} AS REAL)) DESC`)

    return results.map(row => ({
      purpose: row.purpose,
      count: row.count,
      totalAmount: row.totalAmount || 0,
    }))
  }

  private mapToTransaction(row: TransactionRow): Transaction {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { deletedAt, lastSyncAt, ...transaction } = row
    return transaction
  }

  private invalidateCache(): void {
    this.cache.clear()
  }
}