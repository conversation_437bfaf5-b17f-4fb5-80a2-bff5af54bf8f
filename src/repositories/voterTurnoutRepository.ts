import type { InferSelectModel } from 'drizzle-orm'
import { eq, and, sql, inArray } from 'drizzle-orm'
import { voterTurnout } from '@/db/schema'
import type { drizzle } from 'drizzle-orm/better-sqlite3'
import { lru as createLRU } from 'tiny-lru'
import { z } from 'zod'

export type DB = ReturnType<typeof drizzle>

const CreateVoterTurnoutInput = z.object({
  id: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  syncStatus: z.string().min(1),
  voterId: z.string().uuid(),
  electionYear: z.number().int().min(1900).max(2100),
  voted: z.boolean(),
})

const UpdateVoterTurnoutInput = z.object({
  voted: z.boolean().optional(),
  syncStatus: z.string().min(1).optional(),
  updatedAt: z.string(),
})

const VoterTurnoutFilters = z.object({
  syncStatus: z.string().optional(),
  voterId: z.string().uuid().optional(),
  electionYear: z.number().int().optional(),
  voted: z.boolean().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

type VoterTurnoutRow = InferSelectModel<typeof voterTurnout>
type VoterTurnout = Omit<VoterTurnoutRow, 'deletedAt' | 'lastSyncAt'>

interface TurnoutStatistics {
  electionYear: number
  totalVoters: number
  votedCount: number
  notVotedCount: number
  turnoutPercentage: number
}

interface VoterParticipationSummary {
  voterId: string
  totalElections: number
  electionsVoted: number
  participationRate: number
}

export class VoterTurnoutRepository {
  private cache = createLRU<VoterTurnout | null>(2000)

  constructor(private db: DB) {}

  async create(input: z.infer<typeof CreateVoterTurnoutInput>): Promise<VoterTurnout> {
    const data = CreateVoterTurnoutInput.parse(input)

    // Check for unique voter-election constraint
    const existing = await this.findByVoterAndElection(data.voterId, data.electionYear)
    if (existing) {
      throw new Error(`Turnout record already exists for voter ${data.voterId} in election ${data.electionYear}`)
    }

    const row: Omit<VoterTurnoutRow, 'lastSyncAt'> & { lastSyncAt?: string | null } = {
      id: data.id,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: null,
      syncStatus: data.syncStatus,
      lastSyncAt: null,
      voterId: data.voterId,
      electionYear: data.electionYear,
      voted: data.voted,
    }

    await this.db.insert(voterTurnout).values(row).run()
    this.invalidateCache()

    return this.mapToVoterTurnout(row as VoterTurnoutRow)
  }

  async getById(id: string): Promise<VoterTurnout | null> {
    const cacheKey = `turnout:${id}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(voterTurnout)
      .where(and(eq(voterTurnout.id, id), sql`${voterTurnout.deletedAt} IS NULL`))

    const turnout = rows.at(0)
    if (!turnout) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToVoterTurnout(turnout)
    this.cache.set(cacheKey, result)
    return result
  }

  async findByVoterAndElection(voterId: string, electionYear: number): Promise<VoterTurnout | null> {
    const cacheKey = `voter-election:${voterId}:${electionYear}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(voterTurnout)
      .where(and(
        eq(voterTurnout.voterId, voterId),
        eq(voterTurnout.electionYear, electionYear),
        sql`${voterTurnout.deletedAt} IS NULL`
      ))

    const turnout = rows.at(0)
    if (!turnout) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToVoterTurnout(turnout)
    this.cache.set(cacheKey, result)
    return result
  }

  async update(id: string, patch: z.infer<typeof UpdateVoterTurnoutInput>): Promise<boolean> {
    const data = UpdateVoterTurnoutInput.parse(patch)

    const updateData: Record<string, unknown> = {
      updatedAt: data.updatedAt,
    }

    // Only include fields that are provided
    Object.entries(data).forEach(([key, value]) => {
      if (key !== 'updatedAt' && value !== undefined) {
        updateData[key] = value
      }
    })

    const result = await this.db
      .update(voterTurnout)
      .set(updateData)
      .where(and(eq(voterTurnout.id, id), sql`${voterTurnout.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async softDelete(id: string, deletedAt: string): Promise<boolean> {
    const result = await this.db
      .update(voterTurnout)
      .set({ deletedAt, updatedAt: deletedAt })
      .where(and(eq(voterTurnout.id, id), sql`${voterTurnout.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async findMany(filters: z.infer<typeof VoterTurnoutFilters> = {}): Promise<VoterTurnout[]> {
    const validatedFilters = VoterTurnoutFilters.parse(filters)

    const conditions = [sql`${voterTurnout.deletedAt} IS NULL`]

    if (validatedFilters.syncStatus) {
      conditions.push(eq(voterTurnout.syncStatus, validatedFilters.syncStatus))
    }

    if (validatedFilters.voterId) {
      conditions.push(eq(voterTurnout.voterId, validatedFilters.voterId))
    }

    if (validatedFilters.electionYear) {
      conditions.push(eq(voterTurnout.electionYear, validatedFilters.electionYear))
    }

    if (validatedFilters.voted !== undefined) {
      conditions.push(eq(voterTurnout.voted, validatedFilters.voted))
    }

    let query = this.db
      .select()
      .from(voterTurnout)
      .where(and(...conditions))
      .orderBy(sql`${voterTurnout.electionYear} DESC, ${voterTurnout.createdAt} DESC`)

    // Apply pagination
    if (validatedFilters.limit) {
      query = query.limit(validatedFilters.limit)
    }

    if (validatedFilters.offset) {
      query = query.offset(validatedFilters.offset)
    }

    const rows = await query.all()
    return rows.map(row => this.mapToVoterTurnout(row))
  }

  async findByVoter(voterId: string): Promise<VoterTurnout[]> {
    const cacheKey = `voter-turnouts:${voterId}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached as VoterTurnout[]
    }

    const rows = await this.db
      .select()
      .from(voterTurnout)
      .where(and(
        eq(voterTurnout.voterId, voterId),
        sql`${voterTurnout.deletedAt} IS NULL`
      ))
      .orderBy(sql`${voterTurnout.electionYear} DESC`)

    const results = rows.map(row => this.mapToVoterTurnout(row))
    this.cache.set(cacheKey, results)
    return results
  }

  async findByElectionYear(electionYear: number): Promise<VoterTurnout[]> {
    const cacheKey = `election-turnouts:${electionYear}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached as VoterTurnout[]
    }

    const rows = await this.db
      .select()
      .from(voterTurnout)
      .where(and(
        eq(voterTurnout.electionYear, electionYear),
        sql`${voterTurnout.deletedAt} IS NULL`
      ))
      .orderBy(sql`${voterTurnout.createdAt} DESC`)

    const results = rows.map(row => this.mapToVoterTurnout(row))
    this.cache.set(cacheKey, results)
    return results
  }

  async getVotingHistory(voterId: string): Promise<VoterTurnout[]> {
    return this.findByVoter(voterId)
  }

  async getTurnoutPercentage(electionYear: number): Promise<number> {
    const stats = await this.getTurnoutStatistics(electionYear)
    return stats.turnoutPercentage
  }

  async getTurnoutStatistics(electionYear: number): Promise<TurnoutStatistics> {
    const result = await this.db
      .select({
        totalVoters: sql<number>`COUNT(*)`,
        votedCount: sql<number>`SUM(CASE WHEN ${voterTurnout.voted} = 1 THEN 1 ELSE 0 END)`,
        notVotedCount: sql<number>`SUM(CASE WHEN ${voterTurnout.voted} = 0 THEN 1 ELSE 0 END)`,
      })
      .from(voterTurnout)
      .where(and(
        eq(voterTurnout.electionYear, electionYear),
        sql`${voterTurnout.deletedAt} IS NULL`
      ))

    const stats = result[0]
    const totalVoters = stats?.totalVoters || 0
    const votedCount = stats?.votedCount || 0
    const notVotedCount = stats?.notVotedCount || 0
    const turnoutPercentage = totalVoters > 0 ? (votedCount / totalVoters) * 100 : 0

    return {
      electionYear,
      totalVoters,
      votedCount,
      notVotedCount,
      turnoutPercentage: Math.round(turnoutPercentage * 100) / 100, // Round to 2 decimal places
    }
  }

  async getTurnoutComparison(electionYears: number[]): Promise<TurnoutStatistics[]> {
    const comparisons: TurnoutStatistics[] = []

    for (const year of electionYears) {
      const stats = await this.getTurnoutStatistics(year)
      comparisons.push(stats)
    }

    return comparisons.sort((a, b) => b.electionYear - a.electionYear)
  }

  async getVoterParticipationSummary(voterId: string): Promise<VoterParticipationSummary> {
    const result = await this.db
      .select({
        totalElections: sql<number>`COUNT(*)`,
        electionsVoted: sql<number>`SUM(CASE WHEN ${voterTurnout.voted} = 1 THEN 1 ELSE 0 END)`,
      })
      .from(voterTurnout)
      .where(and(
        eq(voterTurnout.voterId, voterId),
        sql`${voterTurnout.deletedAt} IS NULL`
      ))

    const stats = result[0]
    const totalElections = stats?.totalElections || 0
    const electionsVoted = stats?.electionsVoted || 0
    const participationRate = totalElections > 0 ? (electionsVoted / totalElections) * 100 : 0

    return {
      voterId,
      totalElections,
      electionsVoted,
      participationRate: Math.round(participationRate * 100) / 100, // Round to 2 decimal places
    }
  }

  async bulkCreate(turnouts: z.infer<typeof CreateVoterTurnoutInput>[]): Promise<number> {
    if (turnouts.length === 0) {
      return 0
    }

    let created = 0
    for (const turnoutData of turnouts) {
      try {
        await this.create(turnoutData)
        created++
      } catch (error) {
        // Skip duplicates or invalid data
        console.warn(`Failed to create turnout for voter ${turnoutData.voterId}:`, error)
      }
    }

    return created
  }

  async markVotersAsVoted(voterIds: string[], electionYear: number): Promise<number> {
    if (voterIds.length === 0) {
      return 0
    }

    const now = new Date().toISOString()
    const result = await this.db
      .update(voterTurnout)
      .set({
        voted: true,
        updatedAt: now
      })
      .where(and(
        inArray(voterTurnout.voterId, voterIds),
        eq(voterTurnout.electionYear, electionYear),
        sql`${voterTurnout.deletedAt} IS NULL`
      ))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
    }

    return result.changes
  }

  private mapToVoterTurnout(row: VoterTurnoutRow): VoterTurnout {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { deletedAt, lastSyncAt, ...turnout } = row
    return turnout
  }

  private invalidateCache(): void {
    this.cache.clear()
  }
}