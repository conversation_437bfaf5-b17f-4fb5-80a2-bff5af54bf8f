import type { InferSelectModel } from 'drizzle-orm'
import { eq, and, sql, like } from 'drizzle-orm'
import { sections, stations, voters } from '@/db/schema'
import type { drizzle } from 'drizzle-orm/better-sqlite3'
import { lru as createLRU } from 'tiny-lru'
import { z } from 'zod'

export type DB = ReturnType<typeof drizzle>

const CreateSectionInput = z.object({
  id: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  syncStatus: z.string().min(1),
  stationId: z.string().uuid(),
  sectionName: z.string().min(1),
  sectionNumber: z.string().min(1),
})

const UpdateSectionInput = z.object({
  sectionName: z.string().min(1).optional(),
  sectionNumber: z.string().min(1).optional(),
  syncStatus: z.string().min(1).optional(),
  updatedAt: z.string(),
})

const SectionFilters = z.object({
  syncStatus: z.string().optional(),
  stationId: z.string().uuid().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

type SectionRow = InferSelectModel<typeof sections>
type Section = Omit<SectionRow, 'deletedAt' | 'lastSyncAt'>
type StationRow = InferSelectModel<typeof stations>
type Station = Omit<StationRow, 'deletedAt' | 'lastSyncAt'>

export class SectionRepository {
  private cache = createLRU<Section | null>(1000)

  constructor(private db: DB) {}

  async create(input: z.infer<typeof CreateSectionInput>): Promise<Section> {
    const data = CreateSectionInput.parse(input)

    // Check for unique number within station
    const existingSection = await this.findByNumberInStation(
      data.stationId,
      data.sectionNumber
    )
    if (existingSection) {
      throw new Error(
        `Section with number '${data.sectionNumber}' already exists in this station`
      )
    }

    const row: Omit<SectionRow, 'lastSyncAt'> & { lastSyncAt?: string | null } =
      {
        id: data.id,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        deletedAt: null,
        syncStatus: data.syncStatus,
        lastSyncAt: null,
        stationId: data.stationId,
        sectionName: data.sectionName,
        sectionNumber: data.sectionNumber,
      }

    await this.db.insert(sections).values(row).run()
    this.invalidateCache()

    return this.mapToSection(row as SectionRow)
  }

  async getById(id: string): Promise<Section | null> {
    const cacheKey = `section:${id}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(sections)
      .where(and(eq(sections.id, id), sql`${sections.deletedAt} IS NULL`))

    const section = rows.at(0)
    if (!section) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToSection(section)
    this.cache.set(cacheKey, result)
    return result
  }

  async findByNumberInStation(
    stationId: string,
    sectionNumber: string
  ): Promise<Section | null> {
    const cacheKey = `station-number:${stationId}:${sectionNumber}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(sections)
      .where(
        and(
          eq(sections.stationId, stationId),
          eq(sections.sectionNumber, sectionNumber),
          sql`${sections.deletedAt} IS NULL`
        )
      )

    const section = rows.at(0)
    if (!section) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToSection(section)
    this.cache.set(cacheKey, result)
    return result
  }

  async update(
    id: string,
    patch: z.infer<typeof UpdateSectionInput>
  ): Promise<boolean> {
    const data = UpdateSectionInput.parse(patch)

    const updateData: Record<string, unknown> = {
      updatedAt: data.updatedAt,
    }

    // Only include fields that are provided
    Object.entries(data).forEach(([key, value]) => {
      if (key !== 'updatedAt' && value !== undefined) {
        updateData[key] = value
      }
    })

    const result = await this.db
      .update(sections)
      .set(updateData)
      .where(and(eq(sections.id, id), sql`${sections.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async softDelete(id: string, deletedAt: string): Promise<boolean> {
    // Check if there are active voters
    const activeVoters = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(voters)
      .where(and(eq(voters.sectionId, id), sql`${voters.deletedAt} IS NULL`))

    const voterCount = activeVoters[0]?.count || 0
    if (voterCount > 0) {
      throw new Error(
        `Cannot delete section: ${voterCount} active voters exist`
      )
    }

    const result = await this.db
      .update(sections)
      .set({ deletedAt, updatedAt: deletedAt })
      .where(and(eq(sections.id, id), sql`${sections.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async findMany(
    filters: z.infer<typeof SectionFilters> = {}
  ): Promise<Section[]> {
    const validatedFilters = SectionFilters.parse(filters)

    const conditions = [sql`${sections.deletedAt} IS NULL`]

    if (validatedFilters.syncStatus) {
      conditions.push(eq(sections.syncStatus, validatedFilters.syncStatus))
    }

    if (validatedFilters.stationId) {
      conditions.push(eq(sections.stationId, validatedFilters.stationId))
    }

    let query = this.db
      .select()
      .from(sections)
      .where(and(...conditions))

    // Apply pagination
    if (validatedFilters.limit) {
      query = query.limit(validatedFilters.limit)
    }

    if (validatedFilters.offset) {
      query = query.offset(validatedFilters.offset)
    }

    const rows = await query.all()
    return rows.map((row) => this.mapToSection(row))
  }

  async findByStation(stationId: string): Promise<Section[]> {
    const cacheKey = `station-sections:${stationId}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached as Section[]
    }

    const rows = await this.db
      .select()
      .from(sections)
      .where(
        and(
          eq(sections.stationId, stationId),
          sql`${sections.deletedAt} IS NULL`
        )
      )

    const results = rows.map((row) => this.mapToSection(row))
    this.cache.set(cacheKey, results)
    return results
  }

  async searchByName(searchTerm: string): Promise<Section[]> {
    if (!searchTerm.trim()) {
      return []
    }

    const rows = await this.db
      .select()
      .from(sections)
      .where(
        and(
          like(sections.sectionName, `%${searchTerm}%`),
          sql`${sections.deletedAt} IS NULL`
        )
      )

    return rows.map((row) => this.mapToSection(row))
  }

  async getWithStation(
    id: string
  ): Promise<{ section: Section; station: Station } | null> {
    const section = await this.getById(id)
    if (!section) {
      return null
    }

    const stationRows = await this.db
      .select()
      .from(stations)
      .where(
        and(
          eq(stations.id, section.stationId),
          sql`${stations.deletedAt} IS NULL`
        )
      )

    const station = stationRows.at(0)
    if (!station) {
      return null
    }

    return {
      section,
      station: this.mapToStation(station),
    }
  }

  async getWithVoterCount(
    id: string
  ): Promise<{ section: Section; voterCount: number } | null> {
    const section = await this.getById(id)
    if (!section) {
      return null
    }

    const voterCountResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(voters)
      .where(and(eq(voters.sectionId, id), sql`${voters.deletedAt} IS NULL`))

    const voterCount = voterCountResult[0]?.count || 0

    return {
      section,
      voterCount,
    }
  }

  private mapToSection(row: SectionRow): Section {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { deletedAt, lastSyncAt, ...section } = row
    return section
  }

  private mapToStation(row: StationRow): Station {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { deletedAt, lastSyncAt, ...station } = row
    return station
  }

  private invalidateCache(): void {
    this.cache.clear()
  }
}
