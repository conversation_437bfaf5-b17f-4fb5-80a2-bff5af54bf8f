import type { InferSelectModel } from 'drizzle-orm'
import { eq, and, sql } from 'drizzle-orm'
import { voters } from '@/db/schema'
import type { drizzle } from 'drizzle-orm/better-sqlite3'
import { lru as createLRU } from 'tiny-lru'
import { z } from 'zod'

export type DB = ReturnType<typeof drizzle>

const CreateVoterInput = z.object({
  id: z.string().uuid(),
  createdAt: z.string(),
  updatedAt: z.string(),
  syncStatus: z.string().min(1),
  stationId: z.string().uuid(),
  sectionId: z.string().uuid(),
  name: z.string().min(1),
  epicNumber: z.string().min(1),
  houseNumber: z.string().min(1),
  birthYear: z.number().int().min(1900).max(new Date().getFullYear()),
  gender: z.string().min(1),
  relationType: z.string().min(1),
  status: z.string().min(1),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  facebook: z.string().optional(),
  instagram: z.string().optional(),
  twitter: z.string().optional(),
  supporterStatus: z.string().optional(),
  education: z.string().optional(),
  occupation: z.string().optional(),
  community: z.string().optional(),
  religion: z.string().optional(),
  economicStatus: z.string().optional(),
  customNotes: z.string().optional(),
  relationName: z.string().optional(),
})

const UpdateVoterInput = z.object({
  name: z.string().min(1).optional(),
  houseNumber: z.string().min(1).optional(),
  birthYear: z
    .number()
    .int()
    .min(1900)
    .max(new Date().getFullYear())
    .optional(),
  gender: z.string().min(1).optional(),
  relationType: z.string().min(1).optional(),
  status: z.string().min(1).optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  facebook: z.string().optional(),
  instagram: z.string().optional(),
  twitter: z.string().optional(),
  supporterStatus: z.string().optional(),
  education: z.string().optional(),
  occupation: z.string().optional(),
  community: z.string().optional(),
  religion: z.string().optional(),
  economicStatus: z.string().optional(),
  customNotes: z.string().optional(),
  relationName: z.string().optional(),
  updatedAt: z.string(),
})

const VoterFilters = z.object({
  status: z.string().optional(),
  gender: z.string().optional(),
  sectionId: z.string().uuid().optional(),
  stationId: z.string().uuid().optional(),
  birthYearFrom: z.number().int().optional(),
  birthYearTo: z.number().int().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
})

type VoterRow = InferSelectModel<typeof voters>
export type Voter = Omit<VoterRow, 'deletedAt' | 'lastSyncAt'>

export class VoterRepository {
  private cache = createLRU<Voter | null>(1000) // Larger cache for voters

  constructor(private db: DB) {}

  async create(input: z.infer<typeof CreateVoterInput>): Promise<Voter> {
    const data = CreateVoterInput.parse(input)

    const row: Omit<VoterRow, 'lastSyncAt'> & { lastSyncAt?: string | null } = {
      id: data.id,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: null,
      syncStatus: data.syncStatus,
      lastSyncAt: null,
      stationId: data.stationId,
      sectionId: data.sectionId,
      name: data.name,
      epicNumber: data.epicNumber,
      houseNumber: data.houseNumber,
      birthYear: data.birthYear,
      gender: data.gender,
      relationType: data.relationType,
      relationName: data.relationName || null,
      status: data.status,
      phone: data.phone || null,
      email: data.email || null,
      facebook: data.facebook || null,
      instagram: data.instagram || null,
      twitter: data.twitter || null,
      supporterStatus: data.supporterStatus || null,
      education: data.education || null,
      occupation: data.occupation || null,
      community: data.community || null,
      religion: data.religion || null,
      economicStatus: data.economicStatus || null,
      customNotes: data.customNotes || null,
    }

    await this.db.insert(voters).values(row).run()
    this.invalidateCache()

    return this.mapToVoter(row as VoterRow)
  }

  async getById(id: string): Promise<Voter | null> {
    const cacheKey = `voter:${id}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(voters)
      .where(and(eq(voters.id, id), sql`${voters.deletedAt} IS NULL`))

    const voter = rows.at(0)
    if (!voter) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToVoter(voter)
    this.cache.set(cacheKey, result)
    return result
  }

  async findByEpicNumber(epicNumber: string): Promise<Voter | null> {
    const cacheKey = `epic:${epicNumber}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }

    const rows = await this.db
      .select()
      .from(voters)
      .where(
        and(eq(voters.epicNumber, epicNumber), sql`${voters.deletedAt} IS NULL`)
      )

    const voter = rows.at(0)
    if (!voter) {
      this.cache.set(cacheKey, null)
      return null
    }

    const result = this.mapToVoter(voter)
    this.cache.set(cacheKey, result)
    return result
  }

  async update(
    id: string,
    patch: z.infer<typeof UpdateVoterInput>
  ): Promise<boolean> {
    const data = UpdateVoterInput.parse(patch)

    const updateData: Record<string, unknown> = {
      updatedAt: data.updatedAt,
    }

    // Only include fields that are provided
    Object.entries(data).forEach(([key, value]) => {
      if (key !== 'updatedAt' && value !== undefined) {
        updateData[key] = value
      }
    })

    const result = await this.db
      .update(voters)
      .set(updateData)
      .where(and(eq(voters.id, id), sql`${voters.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async softDelete(id: string, deletedAt: string): Promise<boolean> {
    const result = await this.db
      .update(voters)
      .set({ deletedAt, updatedAt: deletedAt })
      .where(and(eq(voters.id, id), sql`${voters.deletedAt} IS NULL`))
      .run()

    if (result.changes > 0) {
      this.invalidateCache()
      return true
    }
    return false
  }

  async findMany(filters: z.infer<typeof VoterFilters> = {}): Promise<Voter[]> {
    const validatedFilters = VoterFilters.parse(filters)

    let query = this.db.select().from(voters)

    // Apply filters
    const conditions = [sql`${voters.deletedAt} IS NULL`]

    if (validatedFilters.status) {
      conditions.push(eq(voters.status, validatedFilters.status))
    }

    if (validatedFilters.gender) {
      conditions.push(eq(voters.gender, validatedFilters.gender))
    }

    if (validatedFilters.sectionId) {
      conditions.push(eq(voters.sectionId, validatedFilters.sectionId))
    }

    if (validatedFilters.stationId) {
      conditions.push(eq(voters.stationId, validatedFilters.stationId))
    }

    if (validatedFilters.birthYearFrom) {
      conditions.push(
        sql`${voters.birthYear} >= ${validatedFilters.birthYearFrom}`
      )
    }

    if (validatedFilters.birthYearTo) {
      conditions.push(
        sql`${voters.birthYear} <= ${validatedFilters.birthYearTo}`
      )
    }

    query = this.db
      .select()
      .from(voters)
      .where(and(...conditions))

    // Apply pagination
    if (validatedFilters.limit) {
      query = query.limit(validatedFilters.limit)
    }

    if (validatedFilters.offset) {
      query = query.offset(validatedFilters.offset)
    }

    const rows = await query.all()
    return rows.map((row) => this.mapToVoter(row))
  }

  async searchByText(searchTerm: string, limit = 50): Promise<Voter[]> {
    if (!searchTerm.trim()) {
      return []
    }

    // Cache key for search results
    const cacheKey = `search:${searchTerm}:${limit}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached as Voter[]
    }

    // Use FTS5 for full-text search with limit for performance
    const searchResults = (await this.db.all(
      sql`
        SELECT v.* FROM voters v
        JOIN voters_search vs ON v.rowid = vs.rowid
        WHERE vs.voters_search MATCH ${searchTerm}
        AND v.deleted_at IS NULL
        ORDER BY vs.rank
        LIMIT ${limit}
      `
    )) as VoterRow[]

    const results = searchResults.map((row) => this.mapToVoter(row))
    this.cache.set(cacheKey, results)
    return results
  }

  private mapToVoter(row: VoterRow): Voter {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { deletedAt, lastSyncAt, ...voter } = row
    return voter
  }

  /**
   * Get voter count by filters for pagination
   */
  async count(filters: z.infer<typeof VoterFilters> = {}): Promise<number> {
    const validatedFilters = VoterFilters.parse(filters)

    const conditions = [sql`${voters.deletedAt} IS NULL`]

    if (validatedFilters.status) {
      conditions.push(eq(voters.status, validatedFilters.status))
    }

    if (validatedFilters.gender) {
      conditions.push(eq(voters.gender, validatedFilters.gender))
    }

    if (validatedFilters.sectionId) {
      conditions.push(eq(voters.sectionId, validatedFilters.sectionId))
    }

    if (validatedFilters.stationId) {
      conditions.push(eq(voters.stationId, validatedFilters.stationId))
    }

    if (validatedFilters.birthYearFrom) {
      conditions.push(
        sql`${voters.birthYear} >= ${validatedFilters.birthYearFrom}`
      )
    }

    if (validatedFilters.birthYearTo) {
      conditions.push(
        sql`${voters.birthYear} <= ${validatedFilters.birthYearTo}`
      )
    }

    const result = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(voters)
      .where(and(...conditions))

    return result[0]?.count || 0
  }

  /**
   * Get voters by section with caching
   */
  async findBySection(sectionId: string, limit = 100): Promise<Voter[]> {
    const cacheKey = `section:${sectionId}:${limit}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached as Voter[]
    }

    const results = await this.findMany({ sectionId, limit })
    this.cache.set(cacheKey, results)
    return results
  }

  /**
   * Get voters by station with caching
   */
  async findByStation(stationId: string, limit = 100): Promise<Voter[]> {
    const cacheKey = `station:${stationId}:${limit}`
    const cached = this.cache.get(cacheKey)
    if (cached !== undefined) {
      return cached as Voter[]
    }

    const results = await this.findMany({ stationId, limit })
    this.cache.set(cacheKey, results)
    return results
  }

  private invalidateCache(): void {
    this.cache.clear()
  }
}
