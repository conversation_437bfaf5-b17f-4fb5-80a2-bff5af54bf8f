import { z } from 'zod'

// Simplified IPC types - removed correlation ID complexity
export type IpcResponse<T = unknown> =
  | { success: true; data: T }
  | {
      success: false
      error: { code: string; message: string; details?: unknown }
    }

// User channel definitions
export const UserChannels = {
  'user:register': 'user:register',
  'user:authenticate': 'user:authenticate',
  'user:validate-session': 'user:validate-session',
  'user:logout': 'user:logout',
  'user:get-by-id': 'user:get-by-id',
  'user:update': 'user:update',
  'user:change-password': 'user:change-password',
  'user:deactivate': 'user:deactivate',
  'user:list': 'user:list',
} as const

export const UserSchemas = {
  registerUser: {
    request: z.object({
      username: z
        .string()
        .min(3)
        .max(50)
        .regex(
          /^[a-zA-Z0-9_]+$/,
          'Username can only contain letters, numbers, and underscores'
        ),
      password: z.string().min(8),
      role: z.enum(['admin', 'operator', 'viewer']),
    }),
    response: z.object({
      id: z.string().uuid(),
      username: z.string(),
      role: z.string(),
      isActive: z.boolean(),
    }),
  },
  authenticateUser: {
    request: z.object({
      username: z.string().min(1),
      password: z.string().min(1),
    }),
    response: z.object({
      success: z.boolean(),
      user: z
        .object({
          id: z.string().uuid(),
          username: z.string(),
          role: z.string(),
          isActive: z.boolean(),
        })
        .nullable(),
      sessionToken: z.string().nullable(),
      error: z.string().optional(),
    }),
  },
  validateSession: {
    request: z.object({
      sessionToken: z.string(),
    }),
    response: z.object({
      valid: z.boolean(),
      user: z
        .object({
          id: z.string().uuid(),
          username: z.string(),
          role: z.string(),
          isActive: z.boolean(),
        })
        .nullable(),
      error: z.string().optional(),
    }),
  },
  logoutUser: {
    request: z.object({
      sessionToken: z.string(),
    }),
    response: z.object({}),
  },
  getUserById: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z
      .object({
        id: z.string().uuid(),
        username: z.string(),
        role: z.string(),
        isActive: z.boolean(),
        createdAt: z.string(),
        updatedAt: z.string(),
      })
      .nullable(),
  },
  updateUser: {
    request: z.object({
      id: z.string().uuid(),
      role: z.enum(['admin', 'operator', 'viewer']).optional(),
      isActive: z.boolean().optional(),
    }),
    response: z
      .object({
        id: z.string().uuid(),
        username: z.string(),
        role: z.string(),
        isActive: z.boolean(),
        createdAt: z.string(),
        updatedAt: z.string(),
      })
      .nullable(),
  },
  changePassword: {
    request: z.object({
      userId: z.string().uuid(),
      currentPassword: z.string().min(1),
      newPassword: z.string().min(8),
    }),
    response: z.boolean(),
  },
  deactivateUser: {
    request: z.object({
      userId: z.string().uuid(),
    }),
    response: z.boolean(),
  },
  listUsers: {
    request: z.object({
      role: z.enum(['admin', 'operator', 'viewer']).optional(),
      isActive: z.boolean().optional(),
      limit: z.number().int().min(1).max(100).optional(),
      offset: z.number().int().min(0).optional(),
    }),
    response: z.array(
      z.object({
        id: z.string().uuid(),
        username: z.string(),
        role: z.string(),
        isActive: z.boolean(),
        createdAt: z.string(),
        updatedAt: z.string(),
      })
    ),
  },
} as const

// Voter channel definitions
export const VoterChannels = {
  'voter:create': 'voter:create',
  'voter:get-by-id': 'voter:get-by-id',
  'voter:update': 'voter:update',
  'voter:delete': 'voter:delete',
  'voter:list': 'voter:list',
  'voter:search': 'voter:search',
  'voter:import': 'voter:import',
} as const

export const VoterSchemas = {
  createVoter: {
    request: z.object({
      pollingStationId: z.string().uuid(),
      sectionId: z.string().uuid(),
      name: z.string().min(1),
      epicNumber: z
        .string()
        .regex(/^[A-Z]{3}[0-9A-Z]{7}$/i, 'Invalid EPIC format'),
      houseNumber: z.string().min(1),
      birthYear: z.number().int().min(1900).max(new Date().getFullYear()),
      gender: z.string().min(1),
      relationshipType: z.string().min(1),
      status: z.string().min(1),
      phone: z.string().optional(),
      email: z.string().email().optional(),
      facebook: z.string().optional(),
      instagram: z.string().optional(),
      twitter: z.string().optional(),
      supporterStatus: z.string().optional(),
      education: z.string().optional(),
      occupation: z.string().optional(),
      community: z.string().optional(),
      religion: z.string().optional(),
      economicStatus: z.string().optional(),
      customNotes: z.string().optional(),
      relationshipName: z.string().optional(),
    }),
    response: z.object({
      id: z.string().uuid(),
      name: z.string(),
      epicNumber: z.string(),
      houseNumber: z.string(),
      birthYear: z.number().int(),
      gender: z.string(),
      relationshipType: z.string(),
      status: z.string(),
      createdAt: z.string(),
      updatedAt: z.string(),
      syncStatus: z.string(),
      pollingStationId: z.string().uuid(),
      sectionId: z.string().uuid(),
      phone: z.string().nullable(),
      email: z.string().nullable(),
      facebook: z.string().nullable(),
      instagram: z.string().nullable(),
      twitter: z.string().nullable(),
      supporterStatus: z.string().nullable(),
      education: z.string().nullable(),
      occupation: z.string().nullable(),
      community: z.string().nullable(),
      religion: z.string().nullable(),
      economicStatus: z.string().nullable(),
      customNotes: z.string().nullable(),
      relationshipName: z.string().nullable(),
    }),
  },
  getVoterById: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z
      .object({
        id: z.string().uuid(),
        name: z.string(),
        epicNumber: z.string(),
        houseNumber: z.string(),
        birthYear: z.number().int(),
        gender: z.string(),
        relationshipType: z.string(),
        status: z.string(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        pollingStationId: z.string().uuid(),
        sectionId: z.string().uuid(),
        phone: z.string().nullable(),
        email: z.string().nullable(),
        facebook: z.string().nullable(),
        instagram: z.string().nullable(),
        twitter: z.string().nullable(),
        supporterStatus: z.string().nullable(),
        education: z.string().nullable(),
        occupation: z.string().nullable(),
        community: z.string().nullable(),
        religion: z.string().nullable(),
        economicStatus: z.string().nullable(),
        customNotes: z.string().nullable(),
        relationshipName: z.string().nullable(),
      })
      .nullable(),
  },
  updateVoter: {
    request: z.object({
      id: z.string().uuid(),
      name: z.string().min(1).optional(),
      epicNumber: z
        .string()
        .regex(/^[A-Z]{3}[0-9A-Z]{7}$/i, 'Invalid EPIC format')
        .optional(),
      houseNumber: z.string().min(1).optional(),
      birthYear: z
        .number()
        .int()
        .min(1900)
        .max(new Date().getFullYear())
        .optional(),
      gender: z.string().min(1).optional(),
      relationshipType: z.string().min(1).optional(),
      status: z.string().min(1).optional(),
      phone: z.string().optional(),
      email: z.string().email().optional(),
      facebook: z.string().optional(),
      instagram: z.string().optional(),
      twitter: z.string().optional(),
      supporterStatus: z.string().optional(),
      education: z.string().optional(),
      occupation: z.string().optional(),
      community: z.string().optional(),
      religion: z.string().optional(),
      economicStatus: z.string().optional(),
      customNotes: z.string().optional(),
      relationshipName: z.string().optional(),
    }),
    response: z.boolean(),
  },
  deleteVoter: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z.boolean(),
  },
  listVoters: {
    request: z.object({
      status: z.string().optional(),
      gender: z.string().optional(),
      sectionId: z.string().uuid().optional(),
      pollingStationId: z.string().uuid().optional(),
      birthYearFrom: z.number().int().optional(),
      birthYearTo: z.number().int().optional(),
      limit: z.number().int().min(1).max(1000).optional(),
      offset: z.number().int().min(0).optional(),
    }),
    response: z.object({
      items: z.array(
        z.object({
          id: z.string().uuid(),
          name: z.string(),
          epicNumber: z.string(),
          houseNumber: z.string(),
          birthYear: z.number().int(),
          gender: z.string(),
          relationshipType: z.string(),
          status: z.string(),
          createdAt: z.string(),
          updatedAt: z.string(),
          syncStatus: z.string(),
          pollingStationId: z.string().uuid(),
          sectionId: z.string().uuid(),
          phone: z.string().nullable(),
          email: z.string().nullable(),
          facebook: z.string().nullable(),
          instagram: z.string().nullable(),
          twitter: z.string().nullable(),
          supporterStatus: z.string().nullable(),
          education: z.string().nullable(),
          occupation: z.string().nullable(),
          community: z.string().nullable(),
          religion: z.string().nullable(),
          economicStatus: z.string().nullable(),
          customNotes: z.string().nullable(),
          relationshipName: z.string().nullable(),
        })
      ),
      total: z.number().int(),
    }),
  },
  searchVoters: {
    request: z.object({
      term: z.string().min(1),
      limit: z.number().int().min(1).max(1000).optional(),
    }),
    response: z.array(
      z.object({
        id: z.string().uuid(),
        name: z.string(),
        epicNumber: z.string(),
        houseNumber: z.string(),
        birthYear: z.number().int(),
        gender: z.string(),
        relationshipType: z.string(),
        status: z.string(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        pollingStationId: z.string().uuid(),
        sectionId: z.string().uuid(),
        phone: z.string().nullable(),
        email: z.string().nullable(),
        facebook: z.string().nullable(),
        instagram: z.string().nullable(),
        twitter: z.string().nullable(),
        supporterStatus: z.string().nullable(),
        education: z.string().nullable(),
        occupation: z.string().nullable(),
        community: z.string().nullable(),
        religion: z.string().nullable(),
        economicStatus: z.string().nullable(),
        customNotes: z.string().nullable(),
        relationshipName: z.string().nullable(),
      })
    ),
  },
  importVoters: {
    request: z.object({
      voters: z.array(
        z.object({
          pollingStationId: z.string().uuid(),
          sectionId: z.string().uuid(),
          name: z.string().min(1),
          epicNumber: z
            .string()
            .regex(/^[A-Z]{3}[0-9A-Z]{7}$/i, 'Invalid EPIC format'),
          houseNumber: z.string().min(1),
          birthYear: z.number().int().min(1900).max(new Date().getFullYear()),
          gender: z.string().min(1),
          relationshipType: z.string().min(1),
          status: z.string().min(1),
          phone: z.string().optional(),
          email: z.string().email().optional(),
          facebook: z.string().optional(),
          instagram: z.string().optional(),
          twitter: z.string().optional(),
          supporterStatus: z.string().optional(),
          education: z.string().optional(),
          occupation: z.string().optional(),
          community: z.string().optional(),
          religion: z.string().optional(),
          economicStatus: z.string().optional(),
          customNotes: z.string().optional(),
          relationshipName: z.string().optional(),
        })
      ),
    }),
    response: z.object({
      processed: z.number().int(),
      succeeded: z.number().int(),
      failed: z.number().int(),
      errors: z.array(
        z.object({
          index: z.number().int(),
          message: z.string(),
        })
      ),
    }),
  },
} as const

// Section channel definitions
export const SectionChannels = {
  'section:create': 'section:create',
  'section:get-by-id': 'section:get-by-id',
  'section:update': 'section:update',
  'section:delete': 'section:delete',
  'section:list': 'section:list',
  'section:get-by-station': 'section:get-by-station',
  'section:search': 'section:search',
  'section:get-with-station': 'section:get-with-station',
  'section:get-with-voter-count': 'section:get-with-voter-count',
} as const

// Polling Station channel definitions
export const PollingStationChannels = {
  'station:create': 'station:create',
  'station:get-by-id': 'station:get-by-id',
  'station:get-by-code': 'station:get-by-code',
  'station:update': 'station:update',
  'station:delete': 'station:delete',
  'station:cascade-delete': 'station:cascade-delete',
  'station:list': 'station:list',
  'station:search': 'station:search',
  'station:get-with-sections': 'station:get-with-sections',
  'station:get-with-voter-count': 'station:get-with-voter-count',
} as const

// Transaction channel definitions
export const TransactionChannels = {
  'transaction:create': 'transaction:create',
  'transaction:get-by-id': 'transaction:get-by-id',
  'transaction:update': 'transaction:update',
  'transaction:delete': 'transaction:delete',
  'transaction:list': 'transaction:list',
  'transaction:get-by-voter': 'transaction:get-by-voter',
  'transaction:get-by-date-range': 'transaction:get-by-date-range',
  'transaction:search-by-purpose': 'transaction:search-by-purpose',
  'transaction:get-history': 'transaction:get-history',
  'transaction:get-total-by-voter': 'transaction:get-total-by-voter',
  'transaction:get-total-by-purpose': 'transaction:get-total-by-purpose',
  'transaction:get-total-by-date-range': 'transaction:get-total-by-date-range',
  'transaction:get-summary-by-purpose': 'transaction:get-summary-by-purpose',
} as const

// Section schemas
export const SectionSchemas = {
  createSection: {
    request: z.object({
      pollingStationId: z.string().uuid(),
      name: z.string().min(1),
      code: z.string().min(1),
    }),
    response: z.object({
      id: z.string().uuid(),
      createdAt: z.string(),
      updatedAt: z.string(),
      syncStatus: z.string(),
      pollingStationId: z.string().uuid(),
      name: z.string(),
      code: z.string(),
    }),
  },
  getSectionById: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z
      .object({
        id: z.string().uuid(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        pollingStationId: z.string().uuid(),
        name: z.string(),
        code: z.string(),
      })
      .nullable(),
  },
  updateSection: {
    request: z.object({
      id: z.string().uuid(),
      name: z.string().min(1).optional(),
      code: z.string().min(1).optional(),
    }),
    response: z.boolean(),
  },
  deleteSection: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z.boolean(),
  },
  listSections: {
    request: z.object({
      syncStatus: z.string().optional(),
      pollingStationId: z.string().uuid().optional(),
      limit: z.number().int().min(1).max(1000).optional(),
      offset: z.number().int().min(0).optional(),
    }),
    response: z.array(
      z.object({
        id: z.string().uuid(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        pollingStationId: z.string().uuid(),
        name: z.string(),
        code: z.string(),
      })
    ),
  },
} as const

// Polling Station schemas
export const PollingStationSchemas = {
  createStation: {
    request: z.object({
      name: z.string().min(1),
      code: z.string().min(1),
    }),
    response: z.object({
      id: z.string().uuid(),
      createdAt: z.string(),
      updatedAt: z.string(),
      syncStatus: z.string(),
      name: z.string(),
      code: z.string(),
    }),
  },
  getStationById: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z
      .object({
        id: z.string().uuid(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        name: z.string(),
        code: z.string(),
      })
      .nullable(),
  },
  updateStation: {
    request: z.object({
      id: z.string().uuid(),
      name: z.string().min(1).optional(),
      code: z.string().min(1).optional(),
    }),
    response: z.boolean(),
  },
  deleteStation: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z.boolean(),
  },
  listStations: {
    request: z.object({
      syncStatus: z.string().optional(),
      limit: z.number().int().min(1).max(1000).optional(),
      offset: z.number().int().min(0).optional(),
    }),
    response: z.array(
      z.object({
        id: z.string().uuid(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        name: z.string(),
        code: z.string(),
      })
    ),
  },
} as const

// Transaction schemas
export const TransactionSchemas = {
  createTransaction: {
    request: z.object({
      voterId: z.string().uuid(),
      date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
      purpose: z.string().min(1),
      amount: z.string().regex(/^\d+\.\d{2}$/),
    }),
    response: z.object({
      id: z.string().uuid(),
      createdAt: z.string(),
      updatedAt: z.string(),
      syncStatus: z.string(),
      voterId: z.string().uuid(),
      date: z.string(),
      purpose: z.string(),
      amount: z.string(),
    }),
  },
  getTransactionById: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z
      .object({
        id: z.string().uuid(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        voterId: z.string().uuid(),
        date: z.string(),
        purpose: z.string(),
        amount: z.string(),
      })
      .nullable(),
  },
  updateTransaction: {
    request: z.object({
      id: z.string().uuid(),
      date: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/)
        .optional(),
      purpose: z.string().min(1).optional(),
      amount: z
        .string()
        .regex(/^\d+\.\d{2}$/)
        .optional(),
    }),
    response: z.boolean(),
  },
  deleteTransaction: {
    request: z.object({
      id: z.string().uuid(),
    }),
    response: z.boolean(),
  },
  listTransactions: {
    request: z.object({
      syncStatus: z.string().optional(),
      voterId: z.string().uuid().optional(),
      dateFrom: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/)
        .optional(),
      dateTo: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/)
        .optional(),
      purpose: z.string().optional(),
      minAmount: z.number().min(0).optional(),
      maxAmount: z.number().min(0).optional(),
      limit: z.number().int().min(1).max(1000).optional(),
      offset: z.number().int().min(0).optional(),
    }),
    response: z.array(
      z.object({
        id: z.string().uuid(),
        createdAt: z.string(),
        updatedAt: z.string(),
        syncStatus: z.string(),
        voterId: z.string().uuid(),
        date: z.string(),
        purpose: z.string(),
        amount: z.string(),
      })
    ),
  },
} as const

// Electoral Management channel definitions
export const ElectoralChannels = {
  'electoral:get-overall-turnout': 'electoral:get-overall-turnout',
  'electoral:record-turnout': 'electoral:record-turnout',
  'electoral:get-daily-summary': 'electoral:get-daily-summary',
} as const

export const ElectoralSchemas = {
  getOverallTurnout: {
    request: z.object({
      electionYear: z.number().int().min(1900).max(2100),
    }),
    response: z.number(),
  },
  recordTurnout: {
    request: z.object({
      electionYear: z.number().int().min(1900).max(2100),
      sectionId: z.string().uuid(),
      stationId: z.string().uuid(),
      count: z.number().int().min(1),
      voterId: z.string().uuid().optional(),
    }),
    response: z.object({
      stationId: z.string().uuid(),
      totalCount: z.number().int(),
      lastUpdatedAt: z.string(),
    }),
  },
  getDailySummary: {
    request: z.object({
      electionYear: z.number().int().min(1900).max(2100),
      date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    }),
    response: z.object({
      date: z.string(),
      totalCount: z.number().int(),
    }),
  },
} as const

// Export all channels
export const IpcChannels = {
  ...UserChannels,
  ...VoterChannels,
  ...SectionChannels,
  ...PollingStationChannels,
  ...TransactionChannels,
  ...ElectoralChannels,
} as const

export type IpcChannel = (typeof IpcChannels)[keyof typeof IpcChannels]
