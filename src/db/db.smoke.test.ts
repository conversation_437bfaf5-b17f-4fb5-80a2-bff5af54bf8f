import { describe, it, expect, beforeAll } from "vitest";
import Database from "better-sqlite3";
import { drizzle } from "drizzle-orm/better-sqlite3";
import { sql } from "drizzle-orm";
// Schema imports not needed in this smoke test (raw SQL DDL used)


function uuid() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

describe("DB smoke", () => {
  let db: ReturnType<typeof drizzle>;

  beforeAll(() => {
    const mem = new Database(":memory:");
    db = drizzle(mem);
    // Apply schema DDL inline (mirrors initial migration sans triggers)
    mem.exec(`
      CREATE TABLE polling_stations (
        id text PRIMARY KEY NOT NULL,
        created_at text NOT NULL,
        updated_at text NOT NULL,
        deleted_at text,
        sync_status text NOT NULL,
        last_sync_at text,
        name text NOT NULL,
        code text NOT NULL
      );
      CREATE UNIQUE INDEX polling_stations_code_unique ON polling_stations (code);
      CREATE TABLE sections (
        id text PRIMARY KEY NOT NULL,
        created_at text NOT NULL,
        updated_at text NOT NULL,
        deleted_at text,
        sync_status text NOT NULL,
        last_sync_at text,
        polling_station_id text NOT NULL,
        name text NOT NULL,
        code text NOT NULL
      );
      CREATE TABLE voters (
        id text PRIMARY KEY NOT NULL,
        created_at text NOT NULL,
        updated_at text NOT NULL,
        deleted_at text,
        sync_status text NOT NULL,
        last_sync_at text,
        polling_station_id text NOT NULL,
        section_id text NOT NULL,
        name text NOT NULL,
        epic_number text NOT NULL,
        house_number text NOT NULL,
        birth_year integer NOT NULL,
        gender text NOT NULL,
        relationship_type text NOT NULL,
        relationship_name text,
        status text NOT NULL,
        phone text,
        email text,
        facebook text,
        instagram text,
        twitter text,
        supporter_status text,
        education text,
        occupation text,
        community text,
        religion text,
        economic_status text,
        custom_notes text
      );
      CREATE UNIQUE INDEX voters_epic_number_unique ON voters (epic_number);
      CREATE VIRTUAL TABLE voters_search USING fts5(
        name, epic_number, house_number, custom_notes, content='voters', content_rowid='rowid'
      );
      CREATE TRIGGER voters_ai AFTER INSERT ON voters BEGIN
        INSERT INTO voters_search(rowid, name, epic_number, house_number, custom_notes)
        VALUES (new.rowid, new.name, new.epic_number, new.house_number, new.custom_notes);
      END;
      CREATE TRIGGER voters_ad AFTER DELETE ON voters BEGIN
        INSERT INTO voters_search(voters_search, rowid, name, epic_number, house_number, custom_notes)
        VALUES('delete', old.rowid, old.name, old.epic_number, old.house_number, old.custom_notes);
      END;
      CREATE TRIGGER voters_au AFTER UPDATE ON voters BEGIN
        INSERT INTO voters_search(voters_search, rowid, name, epic_number, house_number, custom_notes)
        VALUES('delete', old.rowid, old.name, old.epic_number, old.house_number, old.custom_notes);
        INSERT INTO voters_search(rowid, name, epic_number, house_number, custom_notes)
        VALUES (new.rowid, new.name, new.epic_number, new.house_number, new.custom_notes);
      END;
    `);
  });

  it("inserts and enforces unique epic_number, and FTS5 works", async () => {
    const psId = uuid();
    const secId = uuid();
    const voterId = uuid();

    // Insert station and section
    db.run(sql`insert into polling_stations (id, created_at, updated_at, sync_status, name, code)
      values (${psId}, 't', 't', 'pending', 'PS1', 'PSCODE1')`);

    db.run(sql`insert into sections (id, created_at, updated_at, sync_status, polling_station_id, name, code)
      values (${secId}, 't', 't', 'pending', ${psId}, 'S1', '001')`);

    // Insert voter
    db.run(sql`insert into voters (
      id, created_at, updated_at, sync_status, polling_station_id, section_id, name, epic_number,
      house_number, birth_year, gender, relationship_type, status, custom_notes
    ) values (
      ${voterId}, 't', 't', 'pending', ${psId}, ${secId}, 'Alice Johnson', '**********',
      'H-12', 1990, 'Female', 'Father', 'Active', 'note here'
    )`);

    // Unique constraint check
    let uniqueError: unknown = null

    try {
      db.run(sql`insert into voters (
        id, created_at, updated_at, sync_status, polling_station_id, section_id, name, epic_number,
        house_number, birth_year, gender, relationship_type, status
      ) values (
        ${uuid()}, 't', 't', 'pending', ${psId}, ${secId}, 'Duplicate', '**********',
        'H-99', 1980, 'Male', 'Father', 'Active'
      )`);
    } catch (e) {
      uniqueError = e;
    }
    expect(uniqueError).toBeTruthy();

    // FTS5 search
    const res = db.all(
      sql`SELECT rowid, name FROM voters_search WHERE voters_search MATCH 'Alice'`,
    );
    expect(res.length).toBe(1);
    expect(res[0].name).toBe("Alice Johnson");
  });
});
