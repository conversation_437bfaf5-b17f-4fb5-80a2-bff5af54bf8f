import Database from 'better-sqlite3'
import fs from 'node:fs'
import path from 'node:path'

/**
 * Migration runner for applying Drizzle migrations to SQLite database
 * Handles schema creation, FTS5 setup, and migration tracking
 */
export class MigrationRunner {
  constructor(private sqlite: Database.Database) {}

  /**
   * Runs all pending migrations in the correct order
   * Creates migration tracking table if it doesn't exist
   */
  async runMigrations(): Promise<void> {
    // Create migration tracking table
    this.createMigrationTrackingTable()

    const migrationsDir = path.resolve('drizzle')

    if (!fs.existsSync(migrationsDir)) {
      throw new Error(`Migrations directory not found: ${migrationsDir}`)
    }

    // Get all .sql files and sort them to ensure proper order
    const migrationFiles = fs
      .readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort()

    const appliedMigrations = this.getAppliedMigrations()

    for (const file of migrationFiles) {
      // Skip if migration already applied
      if (appliedMigrations.has(file)) {
        continue
      }

      const migrationPath = path.join(migrationsDir, file)
      const migrationSql = fs.readFileSync(migrationPath, 'utf-8')

      try {
        this.executeMigration(migrationSql, file)
        this.recordMigration(file)
      } catch (error) {
        throw new Error(`Failed to execute migration ${file}: ${error}`)
      }
    }
  }

  /**
   * Creates the migration tracking table if it doesn't exist
   */
  private createMigrationTrackingTable(): void {
    const createTableSql = `
      CREATE TABLE IF NOT EXISTS __drizzle_migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        hash TEXT NOT NULL,
        created_at INTEGER NOT NULL
      );
    `
    this.sqlite.exec(createTableSql)
  }

  /**
   * Gets set of already applied migration file names
   */
  private getAppliedMigrations(): Set<string> {
    try {
      const migrations = this.sqlite
        .prepare('SELECT hash FROM __drizzle_migrations')
        .all() as { hash: string }[]

      return new Set(migrations.map(m => m.hash))
    } catch {
      // Table doesn't exist yet, return empty set
      return new Set()
    }
  }

  /**
   * Executes a single migration file
   */
  private executeMigration(migrationSql: string, fileName: string): void {
    // Split by statement separator and execute each statement
    const statements = migrationSql
      .split('--> statement-breakpoint')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)

    // Use transaction for atomicity
    const transaction = this.sqlite.transaction(() => {
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            this.sqlite.exec(statement)
          } catch (error: unknown) {
            // Handle idempotency - ignore "already exists" errors
            const errorMessage = error instanceof Error ? error.message : String(error)
            if (
              errorMessage.includes('already exists') ||
              errorMessage.includes('duplicate column name')
            ) {
              // Skip this statement as it's already applied
              console.warn(`Skipping already applied statement in ${fileName}: ${errorMessage}`)
              continue
            }
            // Re-throw other errors
            throw error
          }
        }
      }
    })

    transaction()
  }

  /**
   * Records a migration as applied
   */
  private recordMigration(fileName: string): void {
    const insertSql = `
      INSERT INTO __drizzle_migrations (hash, created_at)
      VALUES (?, ?)
    `
    this.sqlite.prepare(insertSql).run(fileName, Date.now())
  }

  /**
   * Checks if FTS5 is properly configured
   */
  isFts5Enabled(): boolean {
    try {
      // Check if FTS5 virtual table exists
      const ftsTable = this.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='voters_search'")
        .get() as { name: string } | undefined

      if (!ftsTable) {
        return false
      }

      // Check if it's actually a virtual table with FTS5
      const tableInfo = this.sqlite
        .prepare("SELECT sql FROM sqlite_master WHERE name='voters_search'")
        .get() as { sql: string } | undefined

      return Boolean(tableInfo?.sql?.includes('VIRTUAL TABLE') && tableInfo.sql.includes('fts5'))
    } catch {
      return false
    }
  }

  /**
   * Checks if FTS5 triggers are properly configured
   */
  areFts5TriggersEnabled(): boolean {
    try {
      const triggers = this.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='trigger'")
        .all() as { name: string }[]

      const triggerNames = triggers.map(t => t.name)
      return (
        triggerNames.includes('voters_ai') &&
        triggerNames.includes('voters_ad') &&
        triggerNames.includes('voters_au')
      )
    } catch {
      return false
    }
  }
}