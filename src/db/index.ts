import Database from 'better-sqlite3'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import fs from 'node:fs'
import path from 'node:path'
import { MigrationRunner } from './migrationRunner'

/**
 * Get database path based on environment
 */
function getDatabasePath(): string {
  if (process.env.DB_PATH) {
    return process.env.DB_PATH
  }

  const isTest = process.env.NODE_ENV === 'test'
  const isProduction = process.env.NODE_ENV === 'production'

  if (isTest) {
    return path.join(process.cwd(), 'test-electixir.db')
  } else if (isProduction) {
    return path.join(process.cwd(), 'electixir.db')
  } else {
    // Development environment
    return path.resolve('.data/dev.sqlite')
  }
}

let dbInstance: ReturnType<typeof drizzle> | null = null
let sqliteInstance: Database.Database | null = null

/**
 * Gets the database instance, running migrations on first access
 * Implements singleton pattern to ensure migrations run only once
 */
export async function getDB() {
  if (dbInstance && sqliteInstance) {
    return dbInstance
  }

  const dbPath = getDatabasePath()
  const dir = path.dirname(dbPath)
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }

  sqliteInstance = new Database(dbPath)

  // Run migrations on first database access
  const migrationRunner = new MigrationRunner(sqliteInstance)
  await migrationRunner.runMigrations()

  dbInstance = drizzle(sqliteInstance)
  return dbInstance
}

/**
 * Gets the raw SQLite instance (for advanced operations)
 * Ensures migrations have been run
 */
export async function getSQLite(): Promise<Database.Database> {
  if (!sqliteInstance) {
    await getDB() // This will initialize and run migrations
  }
  return sqliteInstance!
}

/**
 * Synchronous version for IPC handlers (assumes DB is already initialized)
 */
export function getDb() {
  if (!dbInstance) {
    throw new Error('Database not initialized. Call getDB() first.')
  }
  return dbInstance
}

/**
 * Closes the database connection
 * Useful for testing and application shutdown
 */
export async function closeDB(): Promise<void> {
  if (sqliteInstance) {
    sqliteInstance.close()
    sqliteInstance = null
    dbInstance = null
  }
}
