import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core'

// Reusable columns
const id = () => text('id').notNull().primaryKey()
const createdAt = () => text('created_at').notNull()
const updatedAt = () => text('updated_at').notNull()
const deletedAt = () => text('deleted_at') // soft delete
const syncStatus = () => text('sync_status').notNull() // pending|synced|conflict
const lastSyncAt = () => text('last_sync_at')

export const stations = sqliteTable('stations', {
  id: id(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
  syncStatus: syncStatus(),
  lastSyncAt: lastSyncAt(),
  stationName: text('station_name').notNull(),
  stationNumber: text('station_number').notNull().unique(),
})

export const sections = sqliteTable(
  'sections',
  {
    id: id(),
    createdAt: createdAt(),
    updatedAt: updatedAt(),
    deletedAt: deletedAt(),
    syncStatus: syncStatus(),
    lastSyncAt: lastSyncAt(),
    stationId: text('station_id')
      .notNull()
      .references(() => stations.id),
    sectionName: text('section_name').notNull(),
    sectionNumber: text('section_number').notNull(), // unique within station (composite unique)
  },
  (t) => ({
    stationSectionUnique: {
      unique: true,
      columns: [t.stationId, t.sectionNumber],
    },
  })
)

export const voters = sqliteTable('voters', {
  id: id(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
  syncStatus: syncStatus(),
  lastSyncAt: lastSyncAt(),
  stationId: text('station_id')
    .notNull()
    .references(() => stations.id),
  sectionId: text('section_id')
    .notNull()
    .references(() => sections.id),
  name: text('name').notNull(),
  epicNumber: text('epic_number').notNull().unique(),
  houseNumber: text('house_number').notNull(),
  birthYear: integer('birth_year').notNull(),
  gender: text('gender').notNull(),
  relationType: text('relation_type').notNull(),
  relationName: text('relation_name'),
  status: text('status').notNull(),
  phone: text('phone'),
  email: text('email'),
  facebook: text('facebook'),
  instagram: text('instagram'),
  twitter: text('twitter'),
  supporterStatus: text('supporter_status'),
  education: text('education'),
  occupation: text('occupation'),
  community: text('community'),
  religion: text('religion'),
  economicStatus: text('economic_status'),
  customNotes: text('custom_notes'),
})

export const voterTurnout = sqliteTable('voter_turnout', {
  id: id(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
  syncStatus: syncStatus(),
  lastSyncAt: lastSyncAt(),
  voterId: text('voter_id')
    .notNull()
    .references(() => voters.id),
  electionYear: integer('election_year').notNull(),
  voted: integer('voted', { mode: 'boolean' }).notNull(),
})

export const transactions = sqliteTable('transactions', {
  id: id(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
  syncStatus: syncStatus(),
  lastSyncAt: lastSyncAt(),
  voterId: text('voter_id')
    .notNull()
    .references(() => voters.id),
  date: text('date').notNull(),
  purpose: text('purpose').notNull(),
  amount: text('amount').notNull(), // decimal string per spec
})

export const users = sqliteTable('users', {
  id: id(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
  syncStatus: syncStatus(),
  lastSyncAt: lastSyncAt(),
  username: text('username').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  role: text('role').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).notNull(),
  lastLoginAt: text('last_login_at'),
})

export const configOptions = sqliteTable('config_options', {
  id: id(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
  syncStatus: syncStatus(),
  lastSyncAt: lastSyncAt(),
  category: text('category').notNull(),
  value: text('value').notNull(),
  displayOrder: integer('display_order').notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).notNull(),
  optionType: text('option_type').notNull(),
  targetTable: text('target_table'),
})

// FTS5 virtual table for search
export const votersSearch = sqliteTable('voters_search', {
  rowid: integer('rowid').primaryKey(),
  name: text('name'),
  epicNumber: text('epic_number'),
  houseNumber: text('house_number'),
  customNotes: text('custom_notes'),
})
