import { useStations } from './queries/useStations'
import { useSections } from './queries/useSections'

// Types matching your database schema
interface Station {
  id: string
  stationName: string
  stationNumber: string
  createdAt: string
  updatedAt: string
  syncStatus: string
}

interface Section {
  id: string
  stationId: string
  sectionName: string
  sectionNumber: string
  createdAt: string
  updatedAt: string
  syncStatus: string
}

interface UseStationsAndSectionsResult {
  stations: Station[]
  sections: Section[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useStationsAndSections(): UseStationsAndSectionsResult {
  const stationsQuery = useStations()
  const sectionsQuery = useSections()

  const loading = stationsQuery.isLoading || sectionsQuery.isLoading
  const error =
    stationsQuery.error?.message || sectionsQuery.error?.message || null

  const refetch = () => {
    stationsQuery.refetch()
    sectionsQuery.refetch()
  }

  return {
    stations: stationsQuery.data || [],
    sections: sectionsQuery.data || [],
    loading,
    error,
    refetch,
  }
}
