import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
import { useStations, useCreateStation, stationKeys } from '../useStations'
import { useAppStore } from '../../../stores/appStore'

// Mock the IPC renderer
const mockIpcRenderer = {
  invoke: vi.fn(),
}

// Mock window.ipcRenderer
Object.defineProperty(window, 'ipcRenderer', {
  value: mockIpcRenderer,
  writable: true,
})

// Test wrapper with QueryClient
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useStations', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    useAppStore.getState().reset()
  })

  describe('useStations query', () => {
    it('should fetch stations successfully', async () => {
      const mockStations = [
        {
          id: '1',
          stationName: 'Station 1',
          stationNumber: '001',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
          syncStatus: 'synced',
        },
      ]

      mockIpcRenderer.invoke.mockResolvedValue({
        success: true,
        data: mockStations,
      })

      const wrapper = createWrapper()
      const { result } = renderHook(() => useStations(), { wrapper })

      expect(result.current.isLoading).toBe(true)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockStations)
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('station:list', {})
    })

    it('should handle fetch error', async () => {
      mockIpcRenderer.invoke.mockResolvedValue({
        success: false,
        error: { message: 'Database error' },
      })

      const wrapper = createWrapper()
      const { result } = renderHook(() => useStations(), { wrapper })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe('Database error')
    })

    it('should use correct query key', () => {
      const filters = { search: 'test' }
      const expectedKey = stationKeys.list(filters)

      expect(expectedKey).toEqual(['stations', 'list', { filters }])
    })
  })

  describe('useCreateStation mutation', () => {
    it('should create station when online', async () => {
      const newStation = {
        id: '2',
        stationName: 'New Station',
        stationNumber: '002',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
        syncStatus: 'synced',
      }

      mockIpcRenderer.invoke.mockResolvedValue({
        success: true,
        data: newStation,
      })

      const wrapper = createWrapper()
      const { result } = renderHook(() => useCreateStation(), { wrapper })

      expect(result.current.isPending).toBe(false)

      result.current.mutate({
        stationName: 'New Station',
        stationNumber: '002',
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(newStation)
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('station:create', {
        stationName: 'New Station',
        stationNumber: '002',
      })
    })

    it('should queue operation when offline', async () => {
      // Set offline status
      useAppStore.getState().setOnlineStatus(false)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useCreateStation(), { wrapper })

      result.current.mutate({
        stationName: 'Offline Station',
        stationNumber: '003',
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Should not call IPC when offline
      expect(mockIpcRenderer.invoke).not.toHaveBeenCalled()

      // Should add to pending operations
      const { pendingOperations } = useAppStore.getState()
      expect(pendingOperations).toHaveLength(1)
      expect(pendingOperations[0].type).toBe('create')
      expect(pendingOperations[0].entity).toBe('station')
      expect(pendingOperations[0].data).toEqual({
        stationName: 'Offline Station',
        stationNumber: '003',
      })
    })

    it('should handle creation error', async () => {
      mockIpcRenderer.invoke.mockResolvedValue({
        success: false,
        error: { message: 'Validation error' },
      })

      const wrapper = createWrapper()
      const { result } = renderHook(() => useCreateStation(), { wrapper })

      result.current.mutate({
        stationName: 'Invalid Station',
        stationNumber: '004',
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe('Validation error')
    })
  })
})
