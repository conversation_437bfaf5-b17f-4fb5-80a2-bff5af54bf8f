import { useMutationState, useIsMutating } from '@tanstack/react-query'
import { useAppStore } from '../stores/appStore'

export function useMutationStatus() {
  const { isOnline, pendingOperations } = useAppStore()

  // Track all active mutations
  const isMutating = useIsMutating()

  // Track pending mutations (useful for showing loading states)
  const pendingMutations = useMutationState({
    filters: { status: 'pending' },
    select: (mutation) => ({
      mutationKey: mutation.options.mutationKey,
      variables: mutation.state.variables,
      status: mutation.state.status,
    }),
  })

  // Track successful mutations
  const successfulMutations = useMutationState({
    filters: { status: 'success' },
    select: (mutation) => ({
      mutationKey: mutation.options.mutationKey,
      data: mutation.state.data,
      status: mutation.state.status,
    }),
  })

  // Track failed mutations
  const failedMutations = useMutationState({
    filters: { status: 'error' },
    select: (mutation) => ({
      mutationKey: mutation.options.mutationKey,
      error: mutation.state.error,
      status: mutation.state.status,
    }),
  })

  return {
    // Network status
    isOnline,

    // Active mutations
    isMutating: isMutating > 0,
    mutationCount: isMutating,

    // Mutation states
    pendingMutations,
    successfulMutations,
    failedMutations,

    // Offline operations
    pendingOperations,
    hasPendingOperations: pendingOperations.length > 0,

    // Combined status
    isProcessing: isMutating > 0 || pendingOperations.length > 0,
  }
}
