import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
import { useMutationStatus } from '../useMutationStatus'
import { useAppStore } from '../../stores/appStore'

// Test wrapper with QueryClient
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useMutationStatus', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAppStore.getState().reset()
  })

  it('should return initial state', () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useMutationStatus(), { wrapper })

    expect(result.current.isOnline).toBe(true)
    expect(result.current.isMutating).toBe(false)
    expect(result.current.mutationCount).toBe(0)
    expect(result.current.pendingMutations).toEqual([])
    expect(result.current.successfulMutations).toEqual([])
    expect(result.current.failedMutations).toEqual([])
    expect(result.current.pendingOperations).toEqual([])
    expect(result.current.hasPendingOperations).toBe(false)
    expect(result.current.isProcessing).toBe(false)
  })

  it('should reflect offline status', () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useMutationStatus(), { wrapper })

    act(() => {
      useAppStore.getState().setOnlineStatus(false)
    })

    expect(result.current.isOnline).toBe(false)
  })

  it('should track pending operations', () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useMutationStatus(), { wrapper })

    const operation = {
      id: '1',
      type: 'create' as const,
      entity: 'voter' as const,
      data: { name: 'John Doe' },
      timestamp: Date.now(),
    }

    act(() => {
      useAppStore.getState().addPendingOperation(operation)
    })

    expect(result.current.pendingOperations).toHaveLength(1)
    expect(result.current.hasPendingOperations).toBe(true)
    expect(result.current.isProcessing).toBe(true)
  })

  it('should combine mutation and pending operation status', () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useMutationStatus(), { wrapper })

    const operation = {
      id: '1',
      type: 'create' as const,
      entity: 'voter' as const,
      data: { name: 'John Doe' },
      timestamp: Date.now(),
    }

    act(() => {
      useAppStore.getState().addPendingOperation(operation)
    })

    // Even without active mutations, should show as processing due to pending operations
    expect(result.current.isMutating).toBe(false)
    expect(result.current.hasPendingOperations).toBe(true)
    expect(result.current.isProcessing).toBe(true)
  })
})
