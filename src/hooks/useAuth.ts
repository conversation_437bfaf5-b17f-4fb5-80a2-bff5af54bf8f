import { useState, useCallback, useEffect } from 'react'

interface User {
  id: string
  username: string
  role: string
}

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Development bypass - set to true to skip login during development
const DEV_BYPASS_LOGIN = import.meta.env.VITE_DEV_BYPASS_LOGIN === 'true'

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  })

  // Auto-authenticate in development mode
  useEffect(() => {
    if (DEV_BYPASS_LOGIN) {
      const devUser: User = {
        id: 'dev-1',
        username: 'developer',
        role: 'admin',
      }

      setAuthState({
        user: devUser,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      })
    }
  }, [])

  const login = useCallback(
    async (credentials: { username: string; password: string }) => {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }))

      try {
        // TODO: Replace with actual IPC call to authenticate user
        // const user = await window.ipcRenderer.invoke('user:authenticate', credentials)

        // Mock authentication for now
        await new Promise((resolve) => setTimeout(resolve, 1000))

        const mockUser: User = {
          id: '1',
          username: credentials.username,
          role: 'admin',
        }

        setAuthState({
          user: mockUser,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        })
      } catch (error) {
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error:
            error instanceof Error ? error.message : 'Authentication failed',
        }))
      }
    },
    []
  )

  const logout = useCallback(async () => {
    setAuthState((prev) => ({ ...prev, isLoading: true }))

    try {
      // TODO: Replace with actual IPC call to logout user
      // await window.ipcRenderer.invoke('user:logout', { userId: authState.user?.id })

      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      })
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Logout failed',
      }))
    }
  }, [])

  return {
    ...authState,
    login,
    logout,
  }
}
