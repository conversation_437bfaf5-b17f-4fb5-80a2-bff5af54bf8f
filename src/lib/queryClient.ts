import { QueryClient } from '@tanstack/react-query'

export function createQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Cache data for 5 minutes
        staleTime: 5 * 60 * 1000,
        // Keep data in cache for 10 minutes
        gcTime: 10 * 60 * 1000,
        // Retry failed requests 3 times
        retry: 3,
        // Don't refetch on window focus for better offline experience
        refetchOnWindowFocus: false,
        // Always refetch on reconnect for data consistency
        refetchOnReconnect: 'always',
        // Enable offline-first mode for better offline support
        networkMode: 'offlineFirst',
      },
      mutations: {
        // Retry mutations once
        retry: 1,
        // Enable offline-first mode for mutations
        networkMode: 'offlineFirst',
      },
    },
  })
}

// Create a singleton instance
export const queryClient = createQueryClient()
