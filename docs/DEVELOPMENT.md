# Development Guide

## Login Bypass for Development

To skip the login screen during development, you can use the development bypass feature.

### How it works

- The application automatically bypasses the login screen in development mode
- This is controlled by the `VITE_DEV_BYPASS_LOGIN` environment variable in `.env.development`

### Configuration

The `.env.development` file contains:

```
VITE_DEV_BYPASS_LOGIN=true
```

### To disable the bypass

Set `VITE_DEV_BYPASS_LOGIN=false` in `.env.development` or temporarily rename the file.

### Default behavior

- **Development**: Login bypass is enabled by default
- **Production**: Login bypass is always disabled regardless of environment variables

## Development Setup

1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. The app will automatically log in as a developer user with admin privileges
