erDiagram
    STATIONS {
        string id PK "uuid"
        string created_at "timestamp"
        string updated_at "timestamp"
        string deleted_at "soft delete timestamp"
        string sync_status "pending|synced|conflict"
        string last_sync_at "timestamp"
        string station_name "station name"
        string station_number "station number (unique)"
    }

    SECTIONS {
        string id PK "uuid"
        string created_at "timestamp"
        string updated_at "timestamp"
        string deleted_at "soft delete timestamp"
        string sync_status "pending|synced|conflict"
        string last_sync_at "timestamp"
        string station_id FK "-> STATIONS.id"
        string section_name "section name"
        string section_number "section number (unique within station)"
    }

    VOTERS {
        string id PK "uuid"
        string created_at "timestamp"
        string updated_at "timestamp"
        string deleted_at "soft delete timestamp"
        string sync_status "pending|synced|conflict"
        string last_sync_at "timestamp"
        string station_id FK "-> STATIONS.id (match section's station)"
        string section_id FK "-> SECTIONS.id"
        string name "full name (required)"
        string epic_number "voter id (unique, required)"
        string house_number "house/address (required)"
        integer birth_year "year of birth (required)"
        string gender "Male|Female|Other (required)"
        string relationship_type "Father|Mother|Husband|Others (required)"
        string relationship_name "relation name (required)"
        string status "Active|Expired|Shifted|Duplicate|Missing|Disqualified (default: Active, required)"
        string phone "phone"
        string email "email"
        string facebook "facebook"
        string instagram "instagram"
        string twitter "twitter"
        string supporter_status "supporter status"
        string education "education"
        string occupation "occupation"
        string community "community"
        string religion "religion"
        string economic_status "economic status"
        string custom_notes "notes"
    }

    VOTER_TURNOUT {
        string id PK "uuid"
        string created_at "timestamp"
        string updated_at "timestamp"
        string deleted_at "soft delete timestamp"
        string sync_status "pending|synced|conflict"
        string last_sync_at "timestamp"
        string voter_id FK "-> VOTERS.id"
        integer election_year "year"
        boolean voted "0/1"
    }

    TRANSACTIONS {
        string id PK "uuid"
        string created_at "timestamp"
        string updated_at "timestamp"
        string deleted_at "soft delete timestamp"
        string sync_status "pending|synced|conflict"
        string last_sync_at "timestamp"
        string voter_id FK "-> VOTERS.id"
        string date "date"
        string purpose "purpose"
        string amount "decimal string"
    }

    USERS {
        string id PK "uuid"
        string created_at "timestamp"
        string updated_at "timestamp"
        string deleted_at "soft delete timestamp"
        string sync_status "pending|synced|conflict"
        string last_sync_at "timestamp"
        string username "username (unique)"
        string password_hash "password hash"
        string role "owner|admin|editor|viewer"
        boolean is_active "0/1"
        string last_login_at "timestamp"
    }

    CONFIG_OPTIONS {
        string id PK "uuid"
        string created_at "timestamp"
        string updated_at "timestamp"
        string deleted_at "soft delete timestamp"
        string sync_status "pending|synced|conflict"
        string last_sync_at "timestamp"
        string category "category"
        string value "value"
        integer display_order "sort order"
        boolean is_active "0/1"
        string option_type "option type"
        string target_table "target table"
    }

    VOTERS_SEARCH {
        string rowid PK "row id"
        string name "name"
        string epic_number "voter id"
        string house_number "house/address"
        string custom_notes "notes"
    }

    STATIONS ||--o{ SECTIONS : "has many"
    STATIONS ||--o{ VOTERS : "contains"
    SECTIONS ||--o{ VOTERS : "contains"
    VOTERS ||--o{ VOTER_TURNOUT : "has voting history"
    VOTERS ||--o{ TRANSACTIONS : "transactions"
    VOTERS ||--o{ VOTERS_SEARCH : "indexed for search"

    %% Validation & seed notes (concise):
    %% VOTERS required: name, epic_number, house_number, birth_year, gender, relationship_type, status (default: Active).
    %% HARD-CODED ENUMS (not configurable):
    %%   - VOTERS.gender: Male | Female | Other
    %%   - VOTERS.relationship_type: Father | Mother | Husband | Others
    %%   - VOTERS.status: Active | Expired | Shifted | Duplicate | Missing | Disqualified (default Active)
    %%
    %% CONFIGURABLE (managed via CONFIG_OPTIONS; includes Users/roles/policies at app level):
    %%   - education, occupation, community, religion, economic_status, supporter_status, transaction_purpose, form_section
    %%   - user management settings, roles/policies presented via UI (USERS table holds data; behavior is configurable)
    %%
    %% STATIONS.station_number: unique globally.
    %% SECTIONS.section_number: unique within station_id.
    %% VOTERS_SEARCH: optional (use FTS5 or skip).
    %%
    %% CONFIG_OPTIONS seed examples:
    %% education: Primary, Secondary, Graduate, Post Graduate, Illiterate, Technical
    %% occupation: Student, Employee, Business, Farmer, Housewife, Retired, Unemployed, Self Employed, Daily Wage
    %% community: SC, ST, OBC, General, Minority
    %% religion: Christian, Hindu, Muslim, Buddhist
    %% economic_status: BPL, EWS, MIG, HIG
    %% supporter_status: Strong Supporter, Potential, Undecided, Opposed, Neutral
    %% transaction_purpose: Education Support, Emergency Aid, Festival/Event Support, Medical Assistance, Other

    %% VALIDATION (concise; app-level)
    %% name: required; allow letters, spaces, dot (.), apostrophe ('); trim; length 1..100
    %% epic_number: required; pattern ^[A-Z]{3}\d{7}$; unique; trim uppercase
    %% house_number: required; trim; length 1..100
    %% birth_year: required; integer; age between 18..120 based on current year
    %% gender: required; one of Male | Female | Other
    %% relationship_type: required; one of Father | Mother | Husband | Others
    %% relationship_name: optional; same rules as name
    %% status: required; default Active; one of Active | Expired | Shifted | Duplicate | Missing | Disqualified
    %% phone: optional; Indian mobile: ^(\+91[- ]?)?[6-9]\d{9}$
    %% email: optional; standard email format
    %% facebook/instagram/twitter: optional; basic URL/handle validation
    %% supporter_status/education/occupation/community/religion/economic_status: optional; select from CONFIG_OPTIONS
    %% custom_notes: optional; free text; length <= 1000