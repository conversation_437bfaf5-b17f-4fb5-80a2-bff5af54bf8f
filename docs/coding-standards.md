# Electixir - Coding Standards

## Overview

This document defines the coding standards for Electixir, ensuring consistent, maintainable, and high-quality code across the entire application. These standards support our Test-Driven Development, Repository Pattern, and Offline-First architectural principles.

## Core Principles

1. **Test-Driven Development (TDD)**: Write tests first, then implement
2. **Type Safety**: Strict TypeScript with no `any` types
3. **Functional Programming**: Prefer pure functions and immutability
4. **Clean Code**: Self-documenting code with clear naming
5. **Performance**: Optimize for 100k+ records from day one

## Language & Framework Standards

### TypeScript Configuration

```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### Code Formatting

- **Prettier** with 2-space indentation
- **ESLint** with TypeScript rules
- **Trailing commas** for better diffs
- **Single quotes** for strings
- **Semicolons** always required

```typescript
// ✅ Good
const user: User = {
  id: "user_123",
  name: "<PERSON>",
  role: "Admin",
};

// ❌ Bad
const user = {
  id: "user_123",
  name: "John Doe",
  role: "Admin",
};
```

## Naming Conventions

### Variables & Functions

- **camelCase** for variables and functions
- **Descriptive names** that explain purpose
- **Boolean variables** start with `is`, `has`, `can`, `should`

```typescript
// ✅ Good
const isUserActive = true;
const hasVotingHistory = voter.turnout.length > 0;
const canEditVoter = user.role === "Admin";

// ❌ Bad
const active = true;
const history = voter.turnout.length > 0;
const edit = user.role === "Admin";
```

### Types & Interfaces

- **PascalCase** for types, interfaces, and classes
- **Descriptive names** that indicate purpose
- **Suffix interfaces** with purpose when needed

```typescript
// ✅ Good
interface VoterCreateInput {
  name: string;
  epicNumber?: string;
}

type VoterStatus = "Active" | "Expired" | "Shifted";

// ❌ Bad
interface voter {
  name: string;
  epic?: string;
}

type Status = "Active" | "Expired" | "Shifted";
```

### Constants & Enums

- **SCREAMING_SNAKE_CASE** for constants
- **PascalCase** for enum names
- **SCREAMING_SNAKE_CASE** for enum values

```typescript
// ✅ Good
const MAX_VOTERS_PER_PAGE = 100;

enum VoterStatus {
  ACTIVE = "Active",
  EXPIRED = "Expired",
  SHIFTED = "Shifted",
}

// ❌ Bad
const maxVotersPerPage = 100;

enum voterStatus {
  active = "Active",
  expired = "Expired",
}
```

## File & Directory Structure

### File Naming

- **kebab-case** for file names
- **Descriptive names** that indicate purpose
- **Consistent extensions**: `.ts`, `.tsx`, `.test.ts`, `.spec.ts`

```
src/
├── components/
│   ├── voter-list.tsx
│   ├── voter-form.tsx
│   └── voter-search.tsx
├── services/
│   ├── voter.service.ts
│   ├── voter.service.test.ts
│   └── user.service.ts
└── repositories/
    ├── voter.repository.ts
    ├── voter.repository.test.ts
    └── base.repository.ts
```

### Import Organization

- **External libraries** first
- **Internal modules** second
- **Relative imports** last
- **Alphabetical order** within groups

```typescript
// ✅ Good
import { z } from "zod";
import { ulid } from "ulidx";

import { DatabaseAdapter } from "@/lib/database";
import { ValidationPipeline } from "@/lib/validation";

import { BaseRepository } from "./base.repository";

// ❌ Bad
import { BaseRepository } from "./base.repository";
import { z } from "zod";
import { DatabaseAdapter } from "@/lib/database";
```

## Test-Driven Development Standards

### Test Structure

- **Arrange-Act-Assert** pattern
- **Descriptive test names** that explain behavior
- **One assertion per test** when possible
- **Test data factories** for consistent data

```typescript
// ✅ Good
describe("VoterService", () => {
  describe("createVoter", () => {
    it("should create voter with valid input", async () => {
      // Arrange
      const input = createVoterInput({ name: "John Doe" });
      const mockRepo = createMockVoterRepository();
      const service = new VoterService(mockRepo);

      // Act
      const result = await service.createVoter(input);

      // Assert
      expect(result.name).toBe("John Doe");
      expect(mockRepo.create).toHaveBeenCalledWith(
        expect.objectContaining({ name: "John Doe" }),
      );
    });

    it("should throw validation error for invalid input", async () => {
      // Arrange
      const input = createVoterInput({ name: "" });
      const service = new VoterService(createMockVoterRepository());

      // Act & Assert
      await expect(service.createVoter(input)).rejects.toThrow(
        "VALIDATION_ERROR",
      );
    });
  });
});
```

### Test Data Factories

- **Consistent test data** generation
- **Realistic data** that matches production
- **Override capabilities** for specific tests

```typescript
// ✅ Good
export function createVoterInput(
  overrides: Partial<VoterCreateInput> = {},
): VoterCreateInput {
  return {
    name: "John Doe",
    epicNumber: "**********",
    birthYear: 1990,
    ...overrides,
  };
}

// Usage
const input = createVoterInput({ name: "Jane Smith" });
```

## Repository Pattern Standards

### Repository Interface

- **Generic base interface** for common operations
- **Specific methods** for entity-specific queries
- **Consistent error handling** across all repositories

```typescript
// ✅ Good
interface VoterRepository
  extends BaseRepository<
    Voter,
    VoterCreateInput,
    VoterUpdateInput,
    VoterFilters
  > {
  findByEpicNumber(epicNumber: string): Promise<Voter | null>;
  findBySection(sectionId: string): Promise<Voter[]>;
  searchByName(query: string): Promise<Voter[]>;
}

class VoterRepositoryImpl implements VoterRepository {
  async findByEpicNumber(epicNumber: string): Promise<Voter | null> {
    try {
      const result = await this.db
        .select()
        .from(voters)
        .where(eq(voters.epicNumber, epicNumber))
        .get();

      return result || null;
    } catch (error) {
      throw ErrorMapper.mapDatabaseError(error, {
        operation: "findByEpicNumber",
        epicNumber,
      });
    }
  }
}
```

### Service Layer

- **Business logic only** in services
- **Repository dependencies** injected
- **Validation** using Zod schemas
- **Consistent error handling**

```typescript
// ✅ Good
class VoterService {
  constructor(
    private voterRepo: VoterRepository,
    private sectionRepo: SectionRepository,
  ) {}

  async createVoter(input: VoterCreateInput): Promise<Voter> {
    // Validate input
    const validatedInput = ValidationPipeline.validateVoterInput(input);

    // Business logic
    if (validatedInput.epicNumber) {
      const existing = await this.voterRepo.findByEpicNumber(
        validatedInput.epicNumber,
      );
      if (existing) {
        throw ErrorFactory.conflict("EPIC number already exists");
      }
    }

    // Create voter
    return this.voterRepo.create({
      ...validatedInput,
      id: ulid(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }
}
```

## React Component Standards

### Component Structure

- **Functional components** only
- **TypeScript props** with interfaces
- **Custom hooks** for logic extraction
- **Consistent prop naming**

```typescript
// ✅ Good
interface VoterListProps {
  voters: Voter[]
  onVoterSelect: (voter: Voter) => void
  onVoterEdit: (voterId: string) => void
  loading?: boolean
}

export function VoterList({ voters, onVoterSelect, onVoterEdit, loading = false }: VoterListProps) {
  const { selectedVoters, toggleSelection } = useVoterSelection()

  if (loading) {
    return <LoadingSpinner />
  }

  return (
    <div className={styles.voterList}>
      {voters.map(voter => (
        <VoterCard
          key={voter.id}
          voter={voter}
          selected={selectedVoters.has(voter.id)}
          onSelect={() => toggleSelection(voter.id)}
          onEdit={() => onVoterEdit(voter.id)}
        />
      ))}
    </div>
  )
}
```

### State Management

- **TanStack Query** for server state
- **Zustand** for client state
- **Minimal state** - derive when possible
- **Consistent naming** for query keys

```typescript
// ✅ Good
export function useVoters(filters: VoterFilters) {
  return useQuery({
    queryKey: ["voters", filters],
    queryFn: () => voterService.findMany(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Zustand store
interface VoterSelectionState {
  selectedVoters: Set<string>;
  toggleSelection: (voterId: string) => void;
  clearSelection: () => void;
}

export const useVoterSelection = create<VoterSelectionState>((set) => ({
  selectedVoters: new Set(),
  toggleSelection: (voterId) =>
    set((state) => {
      const newSelected = new Set(state.selectedVoters);
      if (newSelected.has(voterId)) {
        newSelected.delete(voterId);
      } else {
        newSelected.add(voterId);
      }
      return { selectedVoters: newSelected };
    }),
  clearSelection: () => set({ selectedVoters: new Set() }),
}));
```

## Error Handling Standards

### Error Types

- **Structured errors** with codes
- **User-friendly messages** for UI
- **Context information** for debugging
- **Consistent error mapping**

```typescript
// ✅ Good
class VoterService {
  async createVoter(input: VoterCreateInput): Promise<Voter> {
    try {
      // Validation
      const validatedInput = ValidationPipeline.validateVoterInput(input);

      // Business logic with specific errors
      if (validatedInput.epicNumber) {
        const existing = await this.voterRepo.findByEpicNumber(
          validatedInput.epicNumber,
        );
        if (existing) {
          throw ErrorFactory.conflict("EPIC number already exists", {
            epicNumber: validatedInput.epicNumber,
            existingVoterId: existing.id,
          });
        }
      }

      return await this.voterRepo.create(validatedInput);
    } catch (error) {
      // Re-throw ApplicationErrors, wrap others
      if (error instanceof ApplicationError) {
        throw error;
      }
      throw ErrorFactory.internal("Failed to create voter", error, { input });
    }
  }
}
```

## Performance Standards

### Database Queries

- **Use indexes** for all WHERE clauses
- **Limit results** with pagination
- **Avoid N+1 queries** with joins
- **Use FTS5** for text search

```typescript
// ✅ Good
async findVotersWithDetails(filters: VoterFilters): Promise<VoterWithDetails[]> {
  const query = this.db
    .select({
      voter: voters,
      section: sections,
      pollingStation: pollingStations,
    })
    .from(voters)
    .leftJoin(sections, eq(voters.sectionId, sections.id))
    .leftJoin(pollingStations, eq(voters.pollingStationId, pollingStations.id))
    .where(eq(voters.deletedAt, null))
    .limit(filters.limit || 50)

  if (filters.search) {
    // Use FTS5 for search
    const searchResults = await this.db
      .select({ id: votersSearch.rowid })
      .from(votersSearch)
      .where(sql`voters_search MATCH ${filters.search}`)

    const ids = searchResults.map(r => r.id.toString())
    query.where(inArray(voters.id, ids))
  }

  return query.all()
}
```

### React Performance

- **Virtual scrolling** for large lists
- **Memoization** for expensive calculations
- **Debounced inputs** for search
- **Code splitting** for routes

```typescript
// ✅ Good
export function VoterList({ voters }: { voters: Voter[] }) {
  const parentRef = useRef<HTMLDivElement>(null)

  const rowVirtualizer = useVirtualizer({
    count: voters.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60,
    overscan: 10,
  })

  return (
    <div ref={parentRef} className="h-96 overflow-auto">
      <div style={{ height: rowVirtualizer.getTotalSize() }}>
        {rowVirtualizer.getVirtualItems().map((virtualItem) => (
          <VoterRow
            key={virtualItem.key}
            voter={voters[virtualItem.index]}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: virtualItem.size,
              transform: `translateY(${virtualItem.start}px)`,
            }}
          />
        ))}
      </div>
    </div>
  )
}
```

## Security Standards

### Input Validation

- **Validate all inputs** with Zod
- **Sanitize user data** before storage
- **Escape output** for display
- **Rate limiting** for API calls

```typescript
// ✅ Good
const VoterCreateSchema = z.object({
  name: z.string().min(1).max(255).trim(),
  epicNumber: z
    .string()
    .regex(/^[A-Z]{3}\d{7}$/)
    .optional(),
  phone: z
    .string()
    .regex(/^\d{10}$/)
    .optional(),
  email: z.string().email().optional(),
});

export function validateVoterInput(input: unknown): VoterCreateInput {
  return VoterCreateSchema.parse(input);
}
```

### Authentication & Authorization

- **Hash passwords** with Argon2id (argon2)
- **Validate sessions** on every request
- **Check permissions** before operations
- **Log security events**

```typescript
// ✅ Good
import argon2 from 'argon2'

class AuthService {
  async authenticateUser(
    username: string,
    password: string,
  ): Promise<AuthResult> {
    const user = await this.userRepo.findByUsername(username);
    if (!user || !user.isActive) {
      // Log failed attempt
      await this.auditLogger.log("AUTH_FAILED", {
        username,
        reason: "user_not_found",
      });
      throw ErrorFactory.unauthorized("Invalid credentials");
    }

    const isValid = await argon2.verify(user.passwordHash, password)
    if (!isValid) {
      await this.auditLogger.log("AUTH_FAILED", {
        username,
        reason: "invalid_password",
      });
      throw ErrorFactory.unauthorized("Invalid credentials");
    }

    await this.auditLogger.log("AUTH_SUCCESS", { userId: user.id });
    return { user: this.sanitizeUser(user) };
  }
}
```

## Documentation Standards

### Code Comments

- **JSDoc** for public APIs
- **Inline comments** for complex logic
- **TODO comments** with issue numbers
- **No obvious comments**

```typescript
// ✅ Good
/**
 * Creates a new voter with validation and duplicate checking
 * @param input - Voter creation data
 * @returns Promise resolving to created voter
 * @throws {ValidationError} When input is invalid
 * @throws {ConflictError} When EPIC number already exists
 */
async createVoter(input: VoterCreateInput): Promise<Voter> {
  // Complex business logic explanation
  // TODO: Add support for bulk voter creation (#123)
}

// ❌ Bad
// This function creates a voter
async createVoter(input: VoterCreateInput): Promise<Voter> {
  // Set the name
  const name = input.name
}
```

### README Files

- **Clear setup instructions**
- **Architecture overview**
- **API documentation**
- **Troubleshooting guide**

## Quality Gates

### Pre-commit Checks

- **TypeScript compilation** must pass
- **ESLint** with zero warnings
- **Prettier** formatting applied
- **Tests** must pass
- **Coverage** above 90%

### CI/CD Pipeline

- **Automated testing** on all PRs
- **Security scanning** for vulnerabilities
- **Performance testing** for regressions
- **Accessibility testing** with axe

### Code Review Requirements

- **Two approvals** required
- **All tests passing**
- **Documentation updated**
- **Performance impact** assessed

This coding standards document ensures consistent, high-quality code that supports our architectural principles and performance requirements.
