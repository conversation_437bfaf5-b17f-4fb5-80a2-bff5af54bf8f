# Electixir - Design Document

## Overview

Electixir represents the optimal architecture for an electoral management system, designed from scratch with modern best practices. The design prioritizes simplicity, testability, and maintainability while providing robust offline-first functionality.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Electron App                             │
├─────────────────────────────────────────────────────────────────┤
│  Renderer Process (React)     │    Main Process (Node.js)       │
│  ┌─────────────────────────┐  │  ┌─────────────────────────────┐ │
│  │     React Components    │  │  │      IPC Handlers          │ │
│  │   ┌─────────────────┐   │  │  │   ┌─────────────────────┐   │ │
│  │   │  TanStack Query │   │  │  │   │     Services        │   │ │
│  │   │   (Server State)│   │  │  │   │  ┌─────────────────┐│   │ │
│  │   └─────────────────┘   │  │  │   │  │  Repositories   ││   │ │
│  │   ┌─────────────────┐   │  │  │   │  │  ┌─────────────┐││   │ │
│  │   │    Zustand      │   │  │  │   │  │  │ Drizzle ORM │││   │ │
│  │   │  (Client State) │   │  │  │   │  │  └─────────────┘││   │ │
│  │   └─────────────────┘   │  │  │   │  └─────────────────┘│   │ │
│  └─────────────────────────┘  │  │   └─────────────────────┘   │ │
├─────────────────────────────────────────────────────────────────┤
│                    Type-Safe IPC Bridge                        │
├─────────────────────────────────────────────────────────────────┤
│                        Data Layer                              │
│  ┌─────────────────────────┐  │  ┌─────────────────────────────┐ │
│  │      SQLite             │  │  │    Cloud Sync               │ │
│  │   (Primary Database)    │  │  │  ┌─────────────────────┐   │ │
│  │                         │  │  │  │     Supabase        │   │ │
│  │  ┌─────────────────┐    │  │  │  │        or           │   │ │
│  │  │   FTS5 Search   │    │  │  │  │    PostgreSQL       │   │ │
│  │  └─────────────────┘    │  │  │  └─────────────────────┘   │ │
│  └─────────────────────────┘  │  └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Layer Responsibilities

#### 1. React Frontend (Renderer Process)

- **Components**: Pure functional components with TypeScript
- **State Management**: TanStack Query for server state, Zustand for client state
- **Forms**: React Hook Form with Zod validation
- **Styling**: CSS Modules with custom accessible components
- **Testing**: React Testing Library with user-centric tests

#### 2. Service Layer (Main Process)

- **Business Logic**: Domain rules and validation
- **IPC Handling**: Type-safe request/response processing
- **Error Handling**: Structured error responses
- **Authentication**: User session management

#### 3. Repository Layer (Main Process)

- **Data Access**: Clean abstraction over database operations
- **Query Optimization**: Efficient database queries with proper indexing
- **Caching**: Transparent caching for frequently accessed data
- **Validation**: Input validation using Zod schemas

#### 4. Data Layer

- **SQLite**: Primary offline database with FTS5 search
- **Drizzle ORM**: Type-safe database operations
- **Cloud Sync**: Bidirectional synchronization with Supabase/PostgreSQL

## Perfect Tech Stack Rationale

### Frontend Choices

**React 18 + TypeScript**

- Mature, well-supported ecosystem
- Excellent TypeScript integration
- Functional components promote testability
- Concurrent features for better UX

**TanStack Query v5**

- Best-in-class server state management
- Built-in caching, background updates, optimistic updates
- Excellent offline support
- Perfect for our offline-first architecture

**Zustand**

- Minimal boilerplate compared to Redux
- Excellent TypeScript support
- Easy to test and reason about
- Perfect for simple client state needs

**React Hook Form + Zod**

- Performant form handling with minimal re-renders
- Zod provides runtime validation and TypeScript types
- Excellent developer experience
- Consistent validation across frontend and backend

**CSS Modules + Custom Components**

- Scoped CSS for maintainable styling
- Custom accessible components following WCAG guidelines
- Consistent design system with CSS variables
- Excellent performance with minimal CSS bundle

### Backend Choices

**Drizzle ORM**

- Type-safe database operations
- Excellent SQLite support
- Minimal runtime overhead
- Great migration system
- Better than Prisma for our use case (lighter, more control)

**better-sqlite3**

- Fastest SQLite driver for Node.js
- Synchronous API (simpler than async for our use case)
- Excellent performance
- Built-in backup and WAL mode support

**Zod**

- Runtime validation with TypeScript inference
- Consistent validation across all layers
- Excellent error messages
- Perfect for IPC validation

### Testing Choices

**Vitest**

- Fast, modern test runner
- Excellent TypeScript support
- Built-in mocking and coverage
- Better than Jest for our stack

**React Testing Library**

- User-centric testing approach
- Encourages accessible components
- Excellent async testing support

**Playwright**

- Modern E2E testing
- Cross-platform support
- Excellent debugging tools

## Data Model Design

### Core Entities

```typescript
// Users (Authentication & Authorization)
interface User {
  id: string
  username: string
  passwordHash: string
  role: 'owner' | 'admin' | 'editor' | 'viewer'
  active: boolean
  lastLoginAt?: Date
  createdAt: Date
  updatedAt: Date
}

// Voters (Core Electoral Data)
interface Voter {
  id: string
  name: string
  epicNumber?: string
  birthYear?: number
  sectionId?: string
  stationId?: string
  createdAt: Date
  updatedAt: Date
}

// Sections (Geographic Organization)
interface Section {
  id: string
  name: string
  description?: string
  createdAt: Date
  updatedAt: Date
}

// Stations
interface Station {
  id: string
  stationName: string
  stationNumber: string
  createdAt: Date
  updatedAt: Date
}

// Turnout Tracking
interface VoterTurnout {
  id: string
  voterId: string
  electionYear: number
  hasVoted: boolean
  votedAt?: Date
  createdAt: Date
  updatedAt: Date
}

// Transactions (Audit Trail)
interface Transaction {
  id: string
  voterId: string
  amount: number
  purpose: string
  date: Date
  notes?: string
  createdAt: Date
  updatedAt: Date
}

// Configuration
interface ConfigOption {
  id: string
  category:
    | 'education'
    | 'occupation'
    | 'community'
    | 'religion'
    | 'economic_status'
    | 'supporter_status'
    | 'transaction_purpose'
  value: string
  displayOrder: number
  active: boolean
  createdAt: Date
  updatedAt: Date
}

// Sync Management (for offline-first)
interface SyncOperation {
  id: string
  entityType: string
  entityId: string
  operation: 'create' | 'update' | 'delete'
  data: Record<string, any>
  timestamp: Date
  synced: boolean
  retryCount: number
  lastError?: string
}
```

### Database Schema (Drizzle)

```typescript
// schema.ts
import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core'

export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  username: text('username').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  role: text('role').notNull(),
  active: integer('active', { mode: 'boolean' }).notNull().default(true),
  lastLoginAt: integer('last_login_at', { mode: 'timestamp' }),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

export const voters = sqliteTable('voters', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  epicNumber: text('epic_number'),
  birthYear: integer('birth_year'),
  sectionId: text('section_id').references(() => sections.id),
  stationId: text('station_id').references(() => stations.id),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// FTS5 virtual table for search
export const votersSearch = sqliteTable('voters_search', {
  rowid: integer('rowid').primaryKey(),
  name: text('name'),
  epicNumber: text('epic_number'),
})

// ... other tables
```

## Components and Interfaces

### Repository Pattern Implementation

```typescript
// Base Repository Interface
interface Repository<T, CreateInput, UpdateInput, Filters> {
  findById(id: string): Promise<T | null>
  findMany(filters: Filters): Promise<T[]>
  create(input: CreateInput): Promise<T>
  update(id: string, input: UpdateInput): Promise<T>
  delete(id: string): Promise<void>
  count(filters?: Partial<Filters>): Promise<number>
}

// Concrete Repository Implementation
class VoterRepository
  implements Repository<Voter, CreateVoterInput, UpdateVoterInput, VoterFilters>
{
  constructor(private db: Database) {}

  async findById(id: string): Promise<Voter | null> {
    const result = await this.db
      .select()
      .from(voters)
      .where(eq(voters.id, id))
      .get()
    return result || null
  }

  async findMany(filters: VoterFilters): Promise<Voter[]> {
    let query = this.db.select().from(voters)

    if (filters.sectionId) {
      query = query.where(eq(voters.sectionId, filters.sectionId))
    }

    if (filters.search) {
      // Use FTS5 for search
      const searchResults = await this.db
        .select()
        .from(votersSearch)
        .where(sql`voters_search MATCH ${filters.search}`)

      const ids = searchResults.map((r) => r.rowid.toString())
      query = query.where(inArray(voters.id, ids))
    }

    return query.limit(filters.limit || 50).all()
  }

  // ... other methods
}
```

### Service Layer Implementation

```typescript
// Service with clean dependencies
class VoterService {
  constructor(
    private voterRepo: VoterRepository,
    private sectionRepo: SectionRepository,
    private validator: VoterValidator
  ) {}

  async createVoter(input: CreateVoterInput): Promise<Voter> {
    // Validate input
    const validatedInput = this.validator.validateCreate(input)

    // Business logic
    if (validatedInput.sectionId) {
      const section = await this.sectionRepo.findById(validatedInput.sectionId)
      if (!section) {
        throw new DomainError('SECTION_NOT_FOUND', 'Section does not exist')
      }
    }

    // Check for duplicates
    if (validatedInput.epicNumber) {
      const existing = await this.voterRepo.findByEpicNumber(
        validatedInput.epicNumber
      )
      if (existing) {
        throw new DomainError('DUPLICATE_EPIC', 'EPIC number already exists')
      }
    }

    // Create voter
    return this.voterRepo.create({
      ...validatedInput,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    })
  }
}
```

### React Component Implementation

```typescript
// Modern React component with hooks
function VoterList() {
  const [filters, setFilters] = useState<VoterFilters>({})

  // Server state with TanStack Query
  const { data: voters, isLoading, error } = useQuery({
    queryKey: ['voters', filters],
    queryFn: () => voterService.findMany(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Client state with Zustand
  const { selectedVoters, toggleSelection } = useVoterSelection()

  if (isLoading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />

  return (
    <div className={styles.voterListContainer}>
      <VoterFilters filters={filters} onChange={setFilters} />
      <VirtualizedTable
        data={voters || []}
        columns={voterColumns}
        onRowSelect={toggleSelection}
        selectedRows={selectedVoters}
      />
    </div>
  )
}
```

## Error Handling

### Structured Error System

```typescript
// Domain Error Types
class DomainError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'DomainError'
  }
}

// Error Handler Middleware
class ErrorHandler {
  static handle(error: unknown): ErrorResponse {
    if (error instanceof DomainError) {
      return {
        type: 'domain',
        code: error.code,
        message: error.message,
        details: error.details,
      }
    }

    if (error instanceof ValidationError) {
      return {
        type: 'validation',
        code: 'VALIDATION_FAILED',
        message: 'Input validation failed',
        details: error.issues,
      }
    }

    // Log unexpected errors
    console.error('Unexpected error:', error)

    return {
      type: 'internal',
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
    }
  }
}
```

## Testing Strategy

### Test-Driven Development Workflow

1. **Write Test First**: Always start with a failing test
2. **Minimal Implementation**: Write just enough code to pass
3. **Refactor**: Improve code while keeping tests green
4. **Repeat**: Continue with next requirement

### Testing Layers

```typescript
// Repository Tests (Integration)
describe('VoterRepository', () => {
  let db: Database
  let repo: VoterRepository

  beforeEach(async () => {
    db = await createTestDatabase()
    repo = new VoterRepository(db)
  })

  it('should create and retrieve voter', async () => {
    const input = createVoterInput({ name: 'John Doe' })
    const created = await repo.create(input)

    expect(created.id).toBeDefined()
    expect(created.name).toBe('John Doe')

    const retrieved = await repo.findById(created.id)
    expect(retrieved).toEqual(created)
  })
})

// Service Tests (Unit)
describe('VoterService', () => {
  let service: VoterService
  let mockVoterRepo: jest.Mocked<VoterRepository>
  let mockSectionRepo: jest.Mocked<SectionRepository>

  beforeEach(() => {
    mockVoterRepo = createMockRepository()
    mockSectionRepo = createMockRepository()
    service = new VoterService(mockVoterRepo, mockSectionRepo, new VoterValidator())
  })

  it('should create voter with valid input', async () => {
    const input = createVoterInput({ name: 'John Doe' })
    mockVoterRepo.create.mockResolvedValue(createVoter(input))

    const result = await service.createVoter(input)

    expect(result.name).toBe('John Doe')
    expect(mockVoterRepo.create).toHaveBeenCalledWith(
      expect.objectContaining({ name: 'John Doe' })
    )
  })
})

// Component Tests (React Testing Library)
describe('VoterList', () => {
  it('should display voters and handle selection', async () => {
    const mockVoters = [createVoter({ name: 'John Doe' })]
    mockVoterService.findMany.mockResolvedValue(mockVoters)

    render(<VoterList />)

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })

    const checkbox = screen.getByRole('checkbox', { name: /select john doe/i })
    await user.click(checkbox)

    expect(checkbox).toBeChecked()
  })
})
```

## Offline-First Implementation

### Sync Strategy

```typescript
class SyncManager {
  constructor(
    private localDb: Database,
    private cloudDb: SupabaseClient,
    private conflictResolver: ConflictResolver
  ) {}

  async syncToCloud(): Promise<void> {
    const pendingOps = await this.getPendingOperations()

    for (const op of pendingOps) {
      try {
        await this.applySyncOperation(op)
        await this.markOperationSynced(op.id)
      } catch (error) {
        await this.handleSyncError(op, error)
      }
    }
  }

  async syncFromCloud(): Promise<void> {
    const lastSync = await this.getLastSyncTimestamp()
    const changes = await this.cloudDb
      .from('voters')
      .select('*')
      .gt('updated_at', lastSync)

    for (const change of changes.data || []) {
      await this.applyCloudChange(change)
    }
  }

  private async applyCloudChange(change: any): Promise<void> {
    const local = await this.localDb
      .select()
      .from(voters)
      .where(eq(voters.id, change.id))
      .get()

    if (local && local.updatedAt > change.updated_at) {
      // Local is newer, resolve conflict
      const resolved = await this.conflictResolver.resolve(local, change)
      await this.updateLocal(resolved)
    } else {
      // Cloud is newer or no local copy
      await this.updateLocal(change)
    }
  }
}
```

## Performance Optimizations

### Database Optimizations

```sql
-- Indexes for common queries
CREATE INDEX idx_voters_section_id ON voters(section_id);
CREATE INDEX idx_voters_epic_number ON voters(epic_number);
CREATE INDEX idx_voter_turnout_voter_id ON voter_turnout(voter_id);
CREATE INDEX idx_voter_turnout_election_year ON voter_turnout(election_year);

-- FTS5 for full-text search
CREATE VIRTUAL TABLE voters_search USING fts5(
  name,
  epic_number,
  content='voters',
  content_rowid='id'
);

-- Triggers to keep FTS5 in sync
CREATE TRIGGER voters_search_insert AFTER INSERT ON voters BEGIN
  INSERT INTO voters_search(rowid, name, epic_number)
  VALUES (new.id, new.name, new.epic_number);
END;
```

### Frontend Optimizations

```typescript
// Virtual scrolling for large lists
function VirtualizedVoterList({ voters }: { voters: Voter[] }) {
  const parentRef = useRef<HTMLDivElement>(null)

  const rowVirtualizer = useVirtualizer({
    count: voters.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60,
    overscan: 10,
  })

  return (
    <div ref={parentRef} className={styles.virtualizedContainer}>
      <div style={{ height: rowVirtualizer.getTotalSize() }}>
        {rowVirtualizer.getVirtualItems().map((virtualItem) => (
          <VoterRow
            key={virtualItem.key}
            voter={voters[virtualItem.index]}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: virtualItem.size,
              transform: `translateY(${virtualItem.start}px)`,
            }}
          />
        ))}
      </div>
    </div>
  )
}
```

This design represents the perfect architecture for an electoral management system, balancing simplicity with powerful features while maintaining excellent developer experience and performance.
