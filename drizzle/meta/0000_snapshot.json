{"version": "6", "dialect": "sqlite", "id": "54541d2a-b636-470c-be2e-7c6ba23631c9", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"config_options": {"name": "config_options", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_sync_at": {"name": "last_sync_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "option_type": {"name": "option_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "target_table": {"name": "target_table", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sections": {"name": "sections", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_sync_at": {"name": "last_sync_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "station_id": {"name": "station_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "section_name": {"name": "section_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "section_number": {"name": "section_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"sections_station_id_stations_id_fk": {"name": "sections_station_id_stations_id_fk", "tableFrom": "sections", "tableTo": "stations", "columnsFrom": ["station_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "stations": {"name": "stations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_sync_at": {"name": "last_sync_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "station_name": {"name": "station_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "station_number": {"name": "station_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"stations_station_number_unique": {"name": "stations_station_number_unique", "columns": ["station_number"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "transactions": {"name": "transactions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_sync_at": {"name": "last_sync_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "voter_id": {"name": "voter_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "purpose": {"name": "purpose", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"transactions_voter_id_voters_id_fk": {"name": "transactions_voter_id_voters_id_fk", "tableFrom": "transactions", "tableTo": "voters", "columnsFrom": ["voter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_sync_at": {"name": "last_sync_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_login_at": {"name": "last_login_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "voter_turnout": {"name": "voter_turnout", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_sync_at": {"name": "last_sync_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "voter_id": {"name": "voter_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "election_year": {"name": "election_year", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "voted": {"name": "voted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"voter_turnout_voter_id_voters_id_fk": {"name": "voter_turnout_voter_id_voters_id_fk", "tableFrom": "voter_turnout", "tableTo": "voters", "columnsFrom": ["voter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "voters": {"name": "voters", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sync_status": {"name": "sync_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_sync_at": {"name": "last_sync_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "station_id": {"name": "station_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "section_id": {"name": "section_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "epic_number": {"name": "epic_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "house_number": {"name": "house_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "birth_year": {"name": "birth_year", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "relation_type": {"name": "relation_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "relation_name": {"name": "relation_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "facebook": {"name": "facebook", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "instagram": {"name": "instagram", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "twitter": {"name": "twitter", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "supporter_status": {"name": "supporter_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "education": {"name": "education", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "occupation": {"name": "occupation", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "community": {"name": "community", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "religion": {"name": "religion", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "economic_status": {"name": "economic_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "custom_notes": {"name": "custom_notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"voters_epic_number_unique": {"name": "voters_epic_number_unique", "columns": ["epic_number"], "isUnique": true}}, "foreignKeys": {"voters_station_id_stations_id_fk": {"name": "voters_station_id_stations_id_fk", "tableFrom": "voters", "tableTo": "stations", "columnsFrom": ["station_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "voters_section_id_sections_id_fk": {"name": "voters_section_id_sections_id_fk", "tableFrom": "voters", "tableTo": "sections", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "voters_search": {"name": "voters_search", "columns": {"rowid": {"name": "rowid", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "epic_number": {"name": "epic_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "house_number": {"name": "house_number", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "custom_notes": {"name": "custom_notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}