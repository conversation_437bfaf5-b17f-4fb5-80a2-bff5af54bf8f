CREATE TABLE `config_options` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	`sync_status` text NOT NULL,
	`last_sync_at` text,
	`category` text NOT NULL,
	`value` text NOT NULL,
	`display_order` integer NOT NULL,
	`is_active` integer NOT NULL,
	`option_type` text NOT NULL,
	`target_table` text
);
--> statement-breakpoint
CREATE TABLE `sections` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	`sync_status` text NOT NULL,
	`last_sync_at` text,
	`station_id` text NOT NULL,
	`section_name` text NOT NULL,
	`section_number` text NOT NULL,
	FOREIGN KEY (`station_id`) REFERENCES `stations`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `stations` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	`sync_status` text NOT NULL,
	`last_sync_at` text,
	`station_name` text NOT NULL,
	`station_number` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `stations_station_number_unique` ON `stations` (`station_number`);--> statement-breakpoint
CREATE TABLE `transactions` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	`sync_status` text NOT NULL,
	`last_sync_at` text,
	`voter_id` text NOT NULL,
	`date` text NOT NULL,
	`purpose` text NOT NULL,
	`amount` text NOT NULL,
	FOREIGN KEY (`voter_id`) REFERENCES `voters`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	`sync_status` text NOT NULL,
	`last_sync_at` text,
	`username` text NOT NULL,
	`password_hash` text NOT NULL,
	`role` text NOT NULL,
	`is_active` integer NOT NULL,
	`last_login_at` text
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_unique` ON `users` (`username`);--> statement-breakpoint
CREATE TABLE `voter_turnout` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	`sync_status` text NOT NULL,
	`last_sync_at` text,
	`voter_id` text NOT NULL,
	`election_year` integer NOT NULL,
	`voted` integer NOT NULL,
	FOREIGN KEY (`voter_id`) REFERENCES `voters`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `voters` (
	`id` text PRIMARY KEY NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	`sync_status` text NOT NULL,
	`last_sync_at` text,
	`station_id` text NOT NULL,
	`section_id` text NOT NULL,
	`name` text NOT NULL,
	`epic_number` text NOT NULL,
	`house_number` text NOT NULL,
	`birth_year` integer NOT NULL,
	`gender` text NOT NULL,
	`relation_type` text NOT NULL,
	`relation_name` text,
	`status` text NOT NULL,
	`phone` text,
	`email` text,
	`facebook` text,
	`instagram` text,
	`twitter` text,
	`supporter_status` text,
	`education` text,
	`occupation` text,
	`community` text,
	`religion` text,
	`economic_status` text,
	`custom_notes` text,
	FOREIGN KEY (`station_id`) REFERENCES `stations`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`section_id`) REFERENCES `sections`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `voters_epic_number_unique` ON `voters` (`epic_number`);--> statement-breakpoint
-- Enable FTS5 virtual table for voter search
CREATE VIRTUAL TABLE voters_search USING fts5(
  name,
  epic_number,
  house_number,
  custom_notes,
  content='voters',
  content_rowid='rowid'
);--> statement-breakpoint
-- Create triggers to keep FTS5 in sync with voters table
CREATE TRIGGER voters_ai AFTER INSERT ON voters BEGIN
  INSERT INTO voters_search(rowid, name, epic_number, house_number, custom_notes)
  VALUES (new.rowid, new.name, new.epic_number, new.house_number, new.custom_notes);
END;--> statement-breakpoint
CREATE TRIGGER voters_ad AFTER DELETE ON voters BEGIN
  INSERT INTO voters_search(voters_search, rowid, name, epic_number, house_number, custom_notes)
  VALUES('delete', old.rowid, old.name, old.epic_number, old.house_number, old.custom_notes);
END;--> statement-breakpoint
CREATE TRIGGER voters_au AFTER UPDATE ON voters BEGIN
  INSERT INTO voters_search(voters_search, rowid, name, epic_number, house_number, custom_notes)
  VALUES('delete', old.rowid, old.name, old.epic_number, old.house_number, old.custom_notes);
  INSERT INTO voters_search(rowid, name, epic_number, house_number, custom_notes)
  VALUES (new.rowid, new.name, new.epic_number, new.house_number, new.custom_notes);
END;--> statement-breakpoint

