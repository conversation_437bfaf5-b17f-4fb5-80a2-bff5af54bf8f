{"name": "electixir-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --config vite.dev.config.ts", "dev:browser": "vite --config vite.preview.config.ts", "dev:full": "vite", "build": "tsc && vite build && electron-builder", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier -w .", "test": "vitest run", "test:ui": "vitest", "coverage": "vitest run --coverage", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "csv:import": "tsx scripts/csv-import.ts import", "csv:sample": "tsx scripts/csv-import.ts sample"}, "dependencies": {"@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@tanstack/react-query": "^5.84.1", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "argon2": "^0.43.1", "better-sqlite3": "^12.2.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "react-router-dom": "^7.7.1", "tiny-lru": "^11.3.4", "uuid": "^11.1.0", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@playwright/test": "^1.54.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/better-sqlite3": "^7.6.13", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.2.4", "csv-parse": "^5.6.0", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-unused-imports": "^4.1.4", "jsdom": "^26.1.0", "playwright": "^1.54.2", "prettier": "^3.6.2", "prettier-plugin-css-order": "^2.1.2", "tsx": "^4.20.3", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "vitest": "^3.2.4"}, "main": "dist-electron/main.js", "drizzle-kit": {"schema": "./src/db/schema.ts", "out": "./drizzle", "driver": "better-sqlite", "dbCredentials": {"url": "./.data/dev.sqlite"}}}