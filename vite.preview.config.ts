import { defineConfig } from "vite";
import path from "node:path";
import react from "@vitejs/plugin-react";

// Browser-only preview config (no Electron)
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    // Mock Electron APIs for browser preview
    'window.ipcRenderer': 'undefined',
  },
  server: {
    port: 5173,
    open: true,
  },
});