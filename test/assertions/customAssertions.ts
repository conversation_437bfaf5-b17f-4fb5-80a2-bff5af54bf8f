import { expect } from 'vitest'
import type { Voter } from '@/shared/schemas/voterSchemas'
import type { Station } from '@/shared/schemas/stationSchemas'
import type { Section } from '@/shared/schemas/sectionSchemas'
import type { User } from '@/shared/schemas/userSchemas'

export function toEqualIgnoringOrder<T>(received: T[], expected: T[]) {
  const pass =
    received.length === expected.length &&
    [...received].sort().every((v, i) => v === [...expected].sort()[i])
  if (pass) {
    return { pass: true, message: () => 'arrays are equal ignoring order' }
  }
  return {
    pass: false,
    message: () =>
      `Expected ${JSON.stringify(received)} to equal ${JSON.stringify(expected)} ignoring order`,
  }
}

// Custom assertions for domain-specific validations
expect.extend({
  toEqualIgnoringOrder,

  /**
   * Assert that a record has valid timestamps
   */
  toHaveValidTimestamps(received: any) {
    const { isNot } = this
    const pass =
      received.created_at &&
      received.updated_at &&
      new Date(received.created_at).getTime() <=
        new Date(received.updated_at).getTime()

    return {
      pass,
      message: () =>
        isNot
          ? `Expected record not to have valid timestamps`
          : `Expected record to have valid timestamps (created_at <= updated_at)`,
    }
  },

  /**
   * Assert that a voter has valid EPIC number format
   */
  toHaveValidEpicNumber(received: Voter) {
    const { isNot } = this
    const epicPattern = /^[A-Z]{3}\d{7,10}$/
    const pass = epicPattern.test(received.epicNumber)

    return {
      pass,
      message: () =>
        isNot
          ? `Expected voter not to have valid EPIC number format`
          : `Expected voter EPIC number "${received.epicNumber}" to match pattern **********`,
    }
  },

  /**
   * Assert that a voter belongs to the correct station and section
   */
  toHaveValidStationSectionRelation(
    received: Voter,
    station: Station,
    section: Section
  ) {
    const { isNot } = this
    const pass =
      received.stationId === station.id &&
      received.sectionId === section.id &&
      section.stationId === station.id

    return {
      pass,
      message: () =>
        isNot
          ? `Expected voter not to have valid station-section relation`
          : `Expected voter to belong to station ${station.id} and section ${section.id}, but got stationId: ${received.stationId}, sectionId: ${received.sectionId}`,
    }
  },

  /**
   * Assert that a record has valid sync status
   */
  toHaveValidSyncStatus(received: any) {
    const { isNot } = this
    const validStatuses = ['pending', 'synced', 'conflict']
    const pass = validStatuses.includes(received.syncStatus)

    return {
      pass,
      message: () =>
        isNot
          ? `Expected record not to have valid sync status`
          : `Expected sync status to be one of ${validStatuses.join(', ')}, but got "${received.syncStatus}"`,
    }
  },

  /**
   * Assert that a user has valid role
   */
  toHaveValidRole(received: User) {
    const { isNot } = this
    const validRoles = ['owner', 'admin', 'editor', 'viewer']
    const pass = validRoles.includes(received.role)

    return {
      pass,
      message: () =>
        isNot
          ? `Expected user not to have valid role`
          : `Expected user role to be one of ${validRoles.join(', ')}, but got "${received.role}"`,
    }
  },

  /**
   * Assert that a voter has valid status
   */
  toHaveValidVoterStatus(received: Voter) {
    const { isNot } = this
    const validStatuses = [
      'Active',
      'Expired',
      'Shifted',
      'Duplicate',
      'Missing',
      'Disqualified',
    ]
    const pass = validStatuses.includes(received.status)

    return {
      pass,
      message: () =>
        isNot
          ? `Expected voter not to have valid status`
          : `Expected voter status to be one of ${validStatuses.join(', ')}, but got "${received.status}"`,
    }
  },

  /**
   * Assert that a record is soft deleted
   */
  toBeSoftDeleted(received: any) {
    const { isNot } = this
    const pass = received.deletedAt !== null && received.deletedAt !== undefined

    return {
      pass,
      message: () =>
        isNot
          ? `Expected record not to be soft deleted`
          : `Expected record to be soft deleted (deletedAt should not be null)`,
    }
  },

  /**
   * Assert that a record is not soft deleted
   */
  toBeActive(received: any) {
    const { isNot } = this
    const pass = received.deletedAt === null

    return {
      pass,
      message: () =>
        isNot
          ? `Expected record not to be active`
          : `Expected record to be active (deletedAt should be null)`,
    }
  },
})
