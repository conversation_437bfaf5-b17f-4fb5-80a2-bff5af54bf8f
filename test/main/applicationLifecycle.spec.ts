import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  ApplicationLifecycle,
  ApplicationConfig,
} from '@/main/applicationLifecycle'

// Mock Electron - moved to top level to avoid hoisting issues
vi.mock('electron', () => ({
  app: {
    whenReady: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    quit: vi.fn(),
    exit: vi.fn(),
    requestSingleInstanceLock: vi.fn().mockReturnValue(true),
  },
  BrowserWindow: vi.fn().mockImplementation(() => ({
    loadURL: vi.fn(),
    loadFile: vi.fn(),
    show: vi.fn(),
    focus: vi.fn(),
    close: vi.fn(),
    isDestroyed: vi.fn().mockReturnValue(false),
    once: vi.fn(),
    on: vi.fn(),
    webContents: {
      openDevTools: vi.fn(),
      on: vi.fn(),
    },
  })),
  dialog: {
    showErrorBox: vi.fn().mockResolvedValue(undefined),
  },
}))

// Mock database
vi.mock('../../src/db', () => ({
  getDB: vi.fn().mockResolvedValue({}),
  closeDB: vi.fn(),
}))

// Mock IPC handlers
vi.mock('../../src/ipc/handlers', () => ({
  initIpcHandlers: vi.fn(),
}))

describe('ApplicationLifecycle (Task 4.3)', () => {
  let applicationLifecycle: ApplicationLifecycle
  let config: ApplicationConfig

  beforeEach(() => {
    config = {
      isDevelopment: true,
      viteDevServerUrl: 'http://localhost:5173',
      rendererDist: '/dist',
      publicDir: '/public',
      preloadPath: '/preload.js',
    }

    applicationLifecycle = new ApplicationLifecycle(config)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Initialization', () => {
    it('should initialize successfully with all components', async () => {
      // Act
      await applicationLifecycle.initialize()

      // Assert
      const healthStatus = applicationLifecycle.getHealthStatus()
      expect(healthStatus.isInitialized).toBe(true)
      expect(healthStatus.isShuttingDown).toBe(false)
    })

    it('should throw error if already initialized', async () => {
      // Arrange
      await applicationLifecycle.initialize()

      // Act & Assert
      await expect(applicationLifecycle.initialize()).rejects.toThrow(
        'Application already initialized'
      )
    })

    it('should handle database initialization failure', async () => {
      // Arrange
      const { getDB } = await import('../../src/db')
      vi.mocked(getDB).mockRejectedValue(
        new Error('Database connection failed')
      )

      // Act & Assert
      await expect(applicationLifecycle.initialize()).rejects.toThrow(
        'Database initialization failed'
      )
    })

    it('should handle IPC handlers initialization failure', async () => {
      // Arrange
      const { initIpcHandlers } = await import('../../src/ipc/handlers')
      vi.mocked(initIpcHandlers).mockImplementation(() => {
        throw new Error('IPC initialization failed')
      })

      // Act & Assert
      await expect(applicationLifecycle.initialize()).rejects.toThrow(
        'IPC handlers initialization failed'
      )
    })
  })

  describe('Application Event Handlers', () => {
    it('should set up application event handlers during initialization', async () => {
      // Act
      await applicationLifecycle.initialize()

      // Assert
      expect(mockApp.whenReady).toHaveBeenCalled()
      expect(mockApp.on).toHaveBeenCalledWith(
        'window-all-closed',
        expect.any(Function)
      )
      expect(mockApp.on).toHaveBeenCalledWith('activate', expect.any(Function))
      expect(mockApp.on).toHaveBeenCalledWith(
        'before-quit',
        expect.any(Function)
      )
    })

    it('should handle window-all-closed event correctly on non-macOS', async () => {
      // Arrange
      const originalPlatform = process.platform
      Object.defineProperty(process, 'platform', { value: 'win32' })

      await applicationLifecycle.initialize()
      const shutdownSpy = vi
        .spyOn(applicationLifecycle, 'shutdown')
        .mockResolvedValue()

      // Get the window-all-closed handler
      const windowAllClosedHandler = mockApp.on.mock.calls.find(
        (call) => call[0] === 'window-all-closed'
      )?.[1]

      // Act
      await windowAllClosedHandler()

      // Assert
      expect(shutdownSpy).toHaveBeenCalled()

      // Cleanup
      Object.defineProperty(process, 'platform', { value: originalPlatform })
    })

    it('should not quit on window-all-closed on macOS', async () => {
      // Arrange
      const originalPlatform = process.platform
      Object.defineProperty(process, 'platform', { value: 'darwin' })

      await applicationLifecycle.initialize()
      const shutdownSpy = vi
        .spyOn(applicationLifecycle, 'shutdown')
        .mockResolvedValue()

      // Get the window-all-closed handler
      const windowAllClosedHandler = mockApp.on.mock.calls.find(
        (call) => call[0] === 'window-all-closed'
      )?.[1]

      // Act
      await windowAllClosedHandler()

      // Assert
      expect(shutdownSpy).not.toHaveBeenCalled()

      // Cleanup
      Object.defineProperty(process, 'platform', { value: originalPlatform })
    })
  })

  describe('Window Management', () => {
    it('should create main window with correct configuration', async () => {
      // Arrange
      await applicationLifecycle.initialize()

      // Trigger window creation
      const readyHandler = mockApp.whenReady.mock.calls[0][0]
      if (typeof readyHandler === 'function') {
        readyHandler()
      }

      // Assert
      const { BrowserWindow } = await import('electron')
      expect(BrowserWindow).toHaveBeenCalledWith(
        expect.objectContaining({
          width: 1200,
          height: 800,
          minWidth: 800,
          minHeight: 600,
          show: false,
          webPreferences: expect.objectContaining({
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: config.preloadPath,
          }),
        })
      )
    })

    it('should load development server URL in development mode', async () => {
      // Arrange
      config.isDevelopment = true
      config.viteDevServerUrl = 'http://localhost:5173'
      applicationLifecycle = new ApplicationLifecycle(config)

      await applicationLifecycle.initialize()

      // Trigger window creation
      const readyHandler = mockApp.whenReady.mock.calls[0][0]
      if (typeof readyHandler === 'function') {
        readyHandler()
      }

      // Assert
      expect(mockBrowserWindow.loadURL).toHaveBeenCalledWith(
        'http://localhost:5173'
      )
    })

    it('should load production build in production mode', async () => {
      // Arrange
      config.isDevelopment = false
      config.viteDevServerUrl = undefined
      applicationLifecycle = new ApplicationLifecycle(config)

      await applicationLifecycle.initialize()

      // Trigger window creation
      const readyHandler = mockApp.whenReady.mock.calls[0][0]
      if (typeof readyHandler === 'function') {
        readyHandler()
      }

      // Assert
      expect(mockBrowserWindow.loadFile).toHaveBeenCalledWith(
        expect.stringContaining('index.html')
      )
    })
  })

  describe('Health Status', () => {
    it('should return correct health status', async () => {
      // Act
      await applicationLifecycle.initialize()
      const healthStatus = applicationLifecycle.getHealthStatus()

      // Assert
      expect(healthStatus).toEqual({
        isInitialized: true,
        isShuttingDown: false,
        hasMainWindow: false, // Window not created yet
        databaseConnected: true,
      })
    })

    it('should return main window instance', async () => {
      // Arrange
      await applicationLifecycle.initialize()

      // Act
      const mainWindow = applicationLifecycle.getMainWindow()

      // Assert
      expect(mainWindow).toBeNull() // Window not created yet in test
    })
  })

  describe('Shutdown', () => {
    it('should shutdown gracefully', async () => {
      // Arrange
      await applicationLifecycle.initialize()

      // Act
      await applicationLifecycle.shutdown()

      // Assert
      const healthStatus = applicationLifecycle.getHealthStatus()
      expect(healthStatus.isShuttingDown).toBe(true)
      expect(mockApp.quit).toHaveBeenCalled()
    })

    it('should not shutdown twice', async () => {
      // Arrange
      await applicationLifecycle.initialize()

      // Act
      await applicationLifecycle.shutdown()
      await applicationLifecycle.shutdown() // Second call should be ignored

      // Assert
      expect(mockApp.quit).toHaveBeenCalledTimes(1)
    })

    it('should handle shutdown errors gracefully', async () => {
      // Arrange
      await applicationLifecycle.initialize()
      mockApp.quit.mockImplementation(() => {
        throw new Error('Quit failed')
      })

      // Act & Assert
      await expect(applicationLifecycle.shutdown()).rejects.toThrow()
      expect(mockApp.exit).toHaveBeenCalledWith(1)
    })
  })

  describe('Error Handling', () => {
    it('should handle critical errors in development mode', async () => {
      // Arrange
      config.isDevelopment = true
      applicationLifecycle = new ApplicationLifecycle(config)
      await applicationLifecycle.initialize()

      // Get the uncaught exception handler
      const uncaughtExceptionHandler = process
        .listeners('uncaughtException')
        .find((listener) =>
          listener.toString().includes('Critical runtime error')
        )

      // Act
      if (uncaughtExceptionHandler) {
        uncaughtExceptionHandler(new Error('Test error'))
      }

      // Assert - In development, app should continue running
      expect(mockApp.exit).not.toHaveBeenCalled()
    })

    it('should handle critical errors in production mode', async () => {
      // Arrange
      config.isDevelopment = false
      applicationLifecycle = new ApplicationLifecycle(config)
      await applicationLifecycle.initialize()

      const shutdownSpy = vi
        .spyOn(applicationLifecycle, 'shutdown')
        .mockResolvedValue()

      // Get the uncaught exception handler
      const uncaughtExceptionHandler = process
        .listeners('uncaughtException')
        .find((listener) =>
          listener.toString().includes('Critical runtime error')
        )

      // Act
      if (uncaughtExceptionHandler) {
        uncaughtExceptionHandler(new Error('Test error'))
      }

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0))

      // Assert - In production, app should shutdown
      expect(shutdownSpy).toHaveBeenCalled()
    })
  })
})
