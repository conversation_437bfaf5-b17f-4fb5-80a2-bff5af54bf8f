import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { BrowserWindow } from 'electron'
import { HealthMonitor } from '../../src/main/healthMonitor'
import { ApplicationLifecycle } from '../../src/main/applicationLifecycle'

// Mock database - moved to top level to avoid hoisting issues
vi.mock('../../src/db', () => ({
  getSQLite: vi.fn().mockResolvedValue({
    prepare: vi.fn().mockReturnValue({
      get: vi.fn().mockReturnValue({ test: 1 }),
    }),
  }),
}))

// Mock application lifecycle
const mockApplicationLifecycle = {
  getHealthStatus: vi.fn().mockReturnValue({
    isInitialized: true,
    isShuttingDown: false,
    hasMainWindow: true,
    databaseConnected: true,
  }),
  getMainWindow: vi.fn().mockReturnValue({
    isDestroyed: vi.fn().mockReturnValue(false),
  }),
} as unknown as ApplicationLifecycle

describe('HealthMonitor (Task 4.3)', () => {
  let healthMonitor: HealthMonitor

  beforeEach(() => {
    healthMonitor = new HealthMonitor(mockApplicationLifecycle, 1000) // 1 second for testing
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    healthMonitor.stop()
    vi.useRealTimers()
    vi.resetAllMocks()
  })

  describe('Initialization', () => {
    it('should start health monitoring', async () => {
      // Arrange
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      // Act
      healthMonitor.start()

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('🏥 Starting health monitor...')
      expect(consoleSpy).toHaveBeenCalledWith(
        '✅ Health monitor started (interval: 1000ms)'
      )

      consoleSpy.mockRestore()
    })

    it('should not start twice', () => {
      // Arrange
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      healthMonitor.start()

      // Act
      healthMonitor.start() // Second call should be ignored

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('🏥 Starting health monitor...')
      expect(consoleSpy).toHaveBeenCalledTimes(3) // Initial message + started message + health check log

      consoleSpy.mockRestore()
    })

    it('should stop health monitoring', () => {
      // Arrange
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      healthMonitor.start()

      // Act
      healthMonitor.stop()

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('🛑 Health monitor stopped')

      consoleSpy.mockRestore()
    })
  })

  describe('Health Checks', () => {
    it('should perform comprehensive health check', async () => {
      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      expect(health).toHaveProperty('overall')
      expect(health).toHaveProperty('checks')
      expect(health).toHaveProperty('uptime')
      expect(health).toHaveProperty('memoryUsage')
      expect(health).toHaveProperty('timestamp')

      expect(health.checks).toHaveLength(4) // application_lifecycle, database, memory_usage, main_window
      expect(health.overall).toBe('healthy')
    })

    it('should check application lifecycle health', async () => {
      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const lifecycleCheck = health.checks.find(
        (check) => check.name === 'application_lifecycle'
      )
      expect(lifecycleCheck).toBeDefined()
      expect(lifecycleCheck?.status).toBe('healthy')
      expect(lifecycleCheck?.message).toBe('Application lifecycle is healthy')
    })

    it('should detect unhealthy application lifecycle', async () => {
      // Arrange
      vi.mocked(mockApplicationLifecycle.getHealthStatus).mockReturnValue({
        isInitialized: false,
        isShuttingDown: false,
        hasMainWindow: false,
        databaseConnected: true,
      })

      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const lifecycleCheck = health.checks.find(
        (check) => check.name === 'application_lifecycle'
      )
      expect(lifecycleCheck?.status).toBe('unhealthy')
      expect(lifecycleCheck?.message).toBe('Application not initialized')
    })

    it('should check database health', async () => {
      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const dbCheck = health.checks.find((check) => check.name === 'database')
      expect(dbCheck).toBeDefined()
      expect(dbCheck?.status).toBe('healthy')
      expect(dbCheck?.message).toBe('Database is responsive')
    })

    it('should detect database connectivity issues', async () => {
      // Arrange
      const { getSQLite } = await import('../../src/db')
      vi.mocked(getSQLite).mockRejectedValue(new Error('Connection failed'))

      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const dbCheck = health.checks.find((check) => check.name === 'database')
      expect(dbCheck?.status).toBe('unhealthy')
      expect(dbCheck?.message).toContain('Database health check failed')
    })

    it('should check memory usage', async () => {
      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const memoryCheck = health.checks.find(
        (check) => check.name === 'memory_usage'
      )
      expect(memoryCheck).toBeDefined()
      expect(memoryCheck?.status).toBe('healthy')
      expect(memoryCheck?.message).toContain('Heap:')
      expect(memoryCheck?.message).toContain('RSS:')
    })

    it('should detect high memory usage', async () => {
      // Arrange - Mock high memory usage
      const originalMemoryUsage = process.memoryUsage
      process.memoryUsage = vi.fn().mockReturnValue({
        rss: 900 * 1024 * 1024, // 900MB (above critical threshold)
        heapTotal: 600 * 1024 * 1024,
        heapUsed: 550 * 1024 * 1024, // 550MB (above critical threshold)
        external: 10 * 1024 * 1024,
        arrayBuffers: 5 * 1024 * 1024,
      })

      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const memoryCheck = health.checks.find(
        (check) => check.name === 'memory_usage'
      )
      expect(memoryCheck?.status).toBe('unhealthy')
      expect(memoryCheck?.message).toContain('Critical memory usage')

      // Cleanup
      process.memoryUsage = originalMemoryUsage
    })

    it('should check main window health', async () => {
      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const windowCheck = health.checks.find(
        (check) => check.name === 'main_window'
      )
      expect(windowCheck).toBeDefined()
      expect(windowCheck?.status).toBe('healthy')
      expect(windowCheck?.message).toBe('Main window is healthy')
    })

    it('should detect destroyed main window', async () => {
      // Arrange
      const mockWindow = {
        isDestroyed: vi.fn().mockReturnValue(true),
      }
      vi.mocked(mockApplicationLifecycle.getMainWindow).mockReturnValue(
        mockWindow as unknown as BrowserWindow
      )

      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const windowCheck = health.checks.find(
        (check) => check.name === 'main_window'
      )
      expect(windowCheck?.status).toBe('unhealthy')
      expect(windowCheck?.message).toBe('Main window is destroyed')
    })

    it('should detect missing main window', async () => {
      // Arrange
      vi.mocked(mockApplicationLifecycle.getMainWindow).mockReturnValue(null)

      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      const windowCheck = health.checks.find(
        (check) => check.name === 'main_window'
      )
      expect(windowCheck?.status).toBe('degraded')
      expect(windowCheck?.message).toBe('Main window not created')
    })
  })

  describe('Overall Health Status', () => {
    it('should report healthy when all checks pass', async () => {
      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      expect(health.overall).toBe('healthy')
    })

    it('should report unhealthy when any check fails', async () => {
      // Arrange
      vi.mocked(mockApplicationLifecycle.getHealthStatus).mockReturnValue({
        isInitialized: false,
        isShuttingDown: false,
        hasMainWindow: false,
        databaseConnected: true,
      })

      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      expect(health.overall).toBe('unhealthy')
    })

    it('should report degraded when checks have unknown status', async () => {
      // Arrange
      const { getSQLite } = await import('../../src/db')
      vi.mocked(getSQLite).mockImplementation(() => {
        throw new Error('Unknown error')
      })

      // Act
      const health = await healthMonitor.performHealthCheck()

      // Assert
      expect(health.overall).toBe('unhealthy') // Database check will be unhealthy, not unknown
    })
  })

  describe('Periodic Monitoring', () => {
    it('should run periodic health checks', async () => {
      // Arrange
      const performHealthCheckSpy = vi.spyOn(
        healthMonitor,
        'performHealthCheck'
      )

      // Act
      healthMonitor.start()

      // Fast-forward time to trigger periodic check
      vi.advanceTimersByTime(1000)
      await vi.runAllTimersAsync()

      // Assert
      expect(performHealthCheckSpy).toHaveBeenCalledTimes(2) // Initial + periodic
    })

    it('should handle errors in periodic checks', async () => {
      // Arrange
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      vi.spyOn(healthMonitor, 'performHealthCheck').mockRejectedValue(
        new Error('Check failed')
      )

      // Act
      healthMonitor.start()
      vi.advanceTimersByTime(1000)
      await vi.runAllTimersAsync()

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        'Periodic health check failed:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('Utility Methods', () => {
    it('should return last health status', async () => {
      // Arrange
      await healthMonitor.performHealthCheck()

      // Act
      const lastStatus = healthMonitor.getLastHealthStatus()

      // Assert
      expect(lastStatus).toBeDefined()
      expect(lastStatus?.overall).toBe('healthy')
    })

    it('should return null for last health status before first check', () => {
      // Act
      const lastStatus = healthMonitor.getLastHealthStatus()

      // Assert
      expect(lastStatus).toBeNull()
    })

    it('should return current uptime', () => {
      // Act
      const uptime = healthMonitor.getUptime()

      // Assert
      expect(uptime).toBeGreaterThanOrEqual(0)
      expect(typeof uptime).toBe('number')
    })
  })
})
