import { describe, it, expect } from 'vitest'
import { ApplicationLifecycle, ApplicationConfig } from '../../src/main/applicationLifecycle'
import { HealthMonitor } from '../../src/main/healthMonitor'

describe('Main Process Integration (Task 4.3)', () => {
  describe('ApplicationLifecycle', () => {
    it('should be importable and instantiable', () => {
      const config: ApplicationConfig = {
        isDevelopment: true,
        viteDevServerUrl: 'http://localhost:5173',
        rendererDist: '/dist',
        publicDir: '/public',
        preloadPath: '/preload.js',
      }

      const lifecycle = new ApplicationLifecycle(config)
      expect(lifecycle).toBeDefined()
      expect(typeof lifecycle.initialize).toBe('function')
      expect(typeof lifecycle.shutdown).toBe('function')
      expect(typeof lifecycle.getHealthStatus).toBe('function')
      expect(typeof lifecycle.getMainWindow).toBe('function')
    })

    it('should have proper health status structure', () => {
      const config: ApplicationConfig = {
        isDevelopment: false,
        rendererDist: '/dist',
        publicDir: '/public',
        preloadPath: '/preload.js',
      }

      const lifecycle = new ApplicationLifecycle(config)
      const healthStatus = lifecycle.getHealthStatus()

      expect(healthStatus).toHaveProperty('isInitialized')
      expect(healthStatus).toHaveProperty('isShuttingDown')
      expect(healthStatus).toHaveProperty('hasMainWindow')
      expect(healthStatus).toHaveProperty('databaseConnected')

      expect(typeof healthStatus.isInitialized).toBe('boolean')
      expect(typeof healthStatus.isShuttingDown).toBe('boolean')
      expect(typeof healthStatus.hasMainWindow).toBe('boolean')
      expect(typeof healthStatus.databaseConnected).toBe('boolean')
    })
  })

  describe('HealthMonitor', () => {
    it('should be importable and instantiable', () => {
      const config: ApplicationConfig = {
        isDevelopment: true,
        viteDevServerUrl: 'http://localhost:5173',
        rendererDist: '/dist',
        publicDir: '/public',
        preloadPath: '/preload.js',
      }

      const lifecycle = new ApplicationLifecycle(config)
      const healthMonitor = new HealthMonitor(lifecycle, 5000)

      expect(healthMonitor).toBeDefined()
      expect(typeof healthMonitor.start).toBe('function')
      expect(typeof healthMonitor.stop).toBe('function')
      expect(typeof healthMonitor.performHealthCheck).toBe('function')
      expect(typeof healthMonitor.getLastHealthStatus).toBe('function')
      expect(typeof healthMonitor.getUptime).toBe('function')
    })

    it('should return null for last health status initially', () => {
      const config: ApplicationConfig = {
        isDevelopment: true,
        viteDevServerUrl: 'http://localhost:5173',
        rendererDist: '/dist',
        publicDir: '/public',
        preloadPath: '/preload.js',
      }

      const lifecycle = new ApplicationLifecycle(config)
      const healthMonitor = new HealthMonitor(lifecycle)

      expect(healthMonitor.getLastHealthStatus()).toBeNull()
    })

    it('should return valid uptime', () => {
      const config: ApplicationConfig = {
        isDevelopment: true,
        viteDevServerUrl: 'http://localhost:5173',
        rendererDist: '/dist',
        publicDir: '/public',
        preloadPath: '/preload.js',
      }

      const lifecycle = new ApplicationLifecycle(config)
      const healthMonitor = new HealthMonitor(lifecycle)

      const uptime = healthMonitor.getUptime()
      expect(typeof uptime).toBe('number')
      expect(uptime).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Main Process Architecture', () => {
    it('should have proper error handling structure', () => {
      // Test that the main process has proper error handling
      expect(typeof process.on).toBe('function')

      // Verify that we can set up error handlers
      const originalListeners = process.listenerCount('uncaughtException')

      const testHandler = () => {}
      process.on('uncaughtException', testHandler)

      expect(process.listenerCount('uncaughtException')).toBe(originalListeners + 1)

      // Cleanup
      process.removeListener('uncaughtException', testHandler)
    })

    it('should have proper shutdown handling structure', () => {
      // Test that the main process can handle shutdown signals
      expect(typeof process.on).toBe('function')

      // Verify that we can set up shutdown handlers
      const originalSIGTERMListeners = process.listenerCount('SIGTERM')
      const originalSIGINTListeners = process.listenerCount('SIGINT')

      const testHandler = () => {}
      process.on('SIGTERM', testHandler)
      process.on('SIGINT', testHandler)

      expect(process.listenerCount('SIGTERM')).toBe(originalSIGTERMListeners + 1)
      expect(process.listenerCount('SIGINT')).toBe(originalSIGINTListeners + 1)

      // Cleanup
      process.removeListener('SIGTERM', testHandler)
      process.removeListener('SIGINT', testHandler)
    })
  })
})