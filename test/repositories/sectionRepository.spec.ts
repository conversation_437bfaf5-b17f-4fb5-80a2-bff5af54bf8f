import { afterEach, beforeAll, beforeEach, describe, expect, it } from 'vitest'
import { createTestDb, TestDb } from '../utils/testDbFactory'
import { pollingStations, voters } from '@/db/schema'
import { SectionRepository } from '@/repositories/sectionRepository'

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('SectionRepository (Task 2.4)', () => {
  let t: TestDb
  let repo: SectionRepository
  let stationId: string

  beforeAll(() => {
    t = createTestDb()
  })

  beforeEach(async () => {
    // Clean all tables between tests
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')

    repo = new SectionRepository(t.db)

    // Set up test polling station
    stationId = uuid()
    const now = new Date().toISOString()

    await t.db.insert(pollingStations).values({
      id: stationId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      name: 'Test Station',
      code: 'TS001',
    })
  })

  afterEach(() => {
    // Clean up after each test
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')
  })

  describe('CRUD Operations', () => {
    it('should create a section with all required fields', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      const sectionData = {
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Section A',
        code: 'SEC-A',
      }

      // Act
      const created = await repo.create(sectionData)

      // Assert
      expect(created.id).toBe(sectionId)
      expect(created.name).toBe('Section A')
      expect(created.code).toBe('SEC-A')
      expect(created.pollingStationId).toBe(stationId)
    })

    it('should retrieve section by ID', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      const sectionData = {
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Section B',
        code: 'SEC-B',
      }

      await repo.create(sectionData)

      // Act
      const retrieved = await repo.getById(sectionId)

      // Assert
      expect(retrieved).not.toBeNull()
      expect(retrieved?.name).toBe('Section B')
      expect(retrieved?.code).toBe('SEC-B')
    })

    it('should update section information', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      const sectionData = {
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Section C',
        code: 'SEC-C',
      }

      await repo.create(sectionData)

      // Act
      const updateData = {
        name: 'Section C Updated',
        updatedAt: new Date().toISOString(),
      }
      const updated = await repo.update(sectionId, updateData)

      // Assert
      expect(updated).toBe(true)
      const retrieved = await repo.getById(sectionId)
      expect(retrieved?.name).toBe('Section C Updated')
    })

    it('should soft delete section', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      const sectionData = {
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Section D',
        code: 'SEC-D',
      }

      await repo.create(sectionData)

      // Act
      const deleted = await repo.softDelete(sectionId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)
      const retrieved = await repo.getById(sectionId)
      expect(retrieved).toBeNull()
    })

    it('should enforce unique code within station constraint', async () => {
      // Arrange
      const now = new Date().toISOString()
      const code = 'SEC-UNIQUE'

      const section1Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'First Section',
        code: code,
      }

      const section2Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Second Section',
        code: code, // Same code in same station
      }

      await repo.create(section1Data)

      // Act & Assert
      await expect(repo.create(section2Data)).rejects.toBeTruthy()
    })

    it('should allow same code in different stations', async () => {
      // Arrange - Create another station
      const station2Id = uuid()
      const now = new Date().toISOString()

      await t.db.insert(pollingStations).values({
        id: station2Id,
        createdAt: now,
        updatedAt: now,
        deletedAt: null,
        syncStatus: 'synced',
        lastSyncAt: null,
        name: 'Test Station 2',
        code: 'TS002',
      })

      const code = 'SEC-SAME'

      const section1Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Section in Station 1',
        code: code,
      }

      const section2Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: station2Id,
        name: 'Section in Station 2',
        code: code, // Same code but different station
      }

      // Act & Assert - Should not throw
      await repo.create(section1Data)
      await repo.create(section2Data)

      const section1 = await repo.getById(section1Data.id)
      const section2 = await repo.getById(section2Data.id)

      expect(section1?.code).toBe(code)
      expect(section2?.code).toBe(code)
    })
  })

  describe('Foreign Key Relationships', () => {
    it('should enforce foreign key constraint with polling station', async () => {
      // Arrange
      const nonExistentStationId = uuid()
      const sectionData = {
        id: uuid(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'pending',
        pollingStationId: nonExistentStationId, // Non-existent station
        name: 'Invalid Section',
        code: 'INVALID',
      }

      // Act & Assert
      await expect(repo.create(sectionData)).rejects.toBeTruthy()
    })

    it('should get section with polling station details', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      const sectionData = {
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Section with Station',
        code: 'SEC-STATION',
      }

      await repo.create(sectionData)

      // Act
      const sectionWithStation = await repo.getWithPollingStation(sectionId)

      // Assert
      expect(sectionWithStation).not.toBeNull()
      expect(sectionWithStation?.section.name).toBe('Section with Station')
      expect(sectionWithStation?.pollingStation.name).toBe('Test Station')
      expect(sectionWithStation?.pollingStation.code).toBe('TS001')
    })

    it('should get section with voter count', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      const sectionData = {
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        name: 'Section with Voters',
        code: 'SEC-VOTERS',
      }

      await repo.create(sectionData)

      // Create some voters in this section
      await t.db.insert(voters).values([
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Voter 1',
          epicNumber: 'EPIC001',
          houseNumber: '1A',
          birthYear: 1990,
          gender: 'Male',
          relationshipType: 'Self',
          relationshipName: null,
          status: 'Active',
          phone: null,
          email: null,
          facebook: null,
          instagram: null,
          twitter: null,
          supporterStatus: null,
          education: null,
          occupation: null,
          community: null,
          religion: null,
          economicStatus: null,
          customNotes: null,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Voter 2',
          epicNumber: 'EPIC002',
          houseNumber: '2B',
          birthYear: 1985,
          gender: 'Female',
          relationshipType: 'Self',
          relationshipName: null,
          status: 'Active',
          phone: null,
          email: null,
          facebook: null,
          instagram: null,
          twitter: null,
          supporterStatus: null,
          education: null,
          occupation: null,
          community: null,
          religion: null,
          economicStatus: null,
          customNotes: null,
        },
      ])

      // Act
      const sectionWithCount = await repo.getWithVoterCount(sectionId)

      // Assert
      expect(sectionWithCount).not.toBeNull()
      expect(sectionWithCount?.voterCount).toBe(2)
    })
  })

  describe('Cascade Operations', () => {
    let sectionId: string

    beforeEach(async () => {
      // Create a section for cascade tests
      sectionId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        pollingStationId: stationId,
        name: 'Cascade Test Section',
        code: 'CTS-SEC',
      })
    })

    it('should prevent deletion when voters exist', async () => {
      // Arrange - Create a voter in the section
      const now = new Date().toISOString()

      await t.db.insert(voters).values({
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        deletedAt: null,
        syncStatus: 'synced',
        lastSyncAt: null,
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'Test Voter',
        epicNumber: 'EPIC001',
        houseNumber: '1A',
        birthYear: 1990,
        gender: 'Male',
        relationshipType: 'Self',
        relationshipName: null,
        status: 'Active',
        phone: null,
        email: null,
        facebook: null,
        instagram: null,
        twitter: null,
        supporterStatus: null,
        education: null,
        occupation: null,
        community: null,
        religion: null,
        economicStatus: null,
        customNotes: null,
      })

      // Act & Assert
      await expect(repo.softDelete(sectionId, new Date().toISOString())).rejects.toBeTruthy()
    })

    it('should allow deletion when no active voters exist', async () => {
      // Act
      const deleted = await repo.softDelete(sectionId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)
      const retrieved = await repo.getById(sectionId)
      expect(retrieved).toBeNull()
    })
  })

  describe('Filtering and Search', () => {
    beforeEach(async () => {
      // Create test sections
      const now = new Date().toISOString()
      const sections = [
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          name: 'Section Alpha',
          code: 'SEC-A',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'pending',
          pollingStationId: stationId,
          name: 'Section Beta',
          code: 'SEC-B',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          name: 'Section Gamma',
          code: 'SEC-G',
        },
      ]

      for (const section of sections) {
        await repo.create(section)
      }
    })

    it('should find sections by polling station', async () => {
      // Act
      const stationSections = await repo.findByPollingStation(stationId)

      // Assert
      expect(stationSections.length).toBe(3)
      stationSections.forEach(section => {
        expect(section.pollingStationId).toBe(stationId)
      })
    })

    it('should find sections by sync status', async () => {
      // Act
      const syncedSections = await repo.findMany({ syncStatus: 'synced' })

      // Assert
      expect(syncedSections.length).toBe(2)
      syncedSections.forEach(section => {
        expect(section.syncStatus).toBe('synced')
      })
    })

    it('should search sections by name', async () => {
      // Act
      const results = await repo.searchByName('Alpha')

      // Assert
      expect(results.length).toBe(1)
      expect(results[0].name).toBe('Section Alpha')
    })

    it('should find section by code within station', async () => {
      // Act
      const section = await repo.findByCodeInStation(stationId, 'SEC-B')

      // Assert
      expect(section).not.toBeNull()
      expect(section?.name).toBe('Section Beta')
    })

    it('should support pagination', async () => {
      // Act
      const page1 = await repo.findMany({ limit: 2, offset: 0 })
      const page2 = await repo.findMany({ limit: 2, offset: 2 })

      // Assert
      expect(page1.length).toBe(2)
      expect(page2.length).toBe(1)

      // Ensure no duplicates between pages
      const page1Ids = page1.map(s => s.id)
      const page2Ids = page2.map(s => s.id)
      const intersection = page1Ids.filter(id => page2Ids.includes(id))
      expect(intersection.length).toBe(0)
    })
  })

  describe('Caching', () => {
    it('should cache frequently accessed sections', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        pollingStationId: stationId,
        name: 'Cached Section',
        code: 'CACHE-SEC',
      })

      // Act - Access same section multiple times
      const firstCall = await repo.getById(sectionId)
      const secondCall = await repo.getById(sectionId)

      // Assert
      expect(firstCall).toEqual(secondCall)
      expect(firstCall?.name).toBe('Cached Section')
    })

    it('should invalidate cache on updates', async () => {
      // Arrange
      const sectionId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        pollingStationId: stationId,
        name: 'Original Section Name',
        code: 'ORIG-SEC',
      })

      // Get initial cached value
      const initial = await repo.getById(sectionId)
      expect(initial?.name).toBe('Original Section Name')

      // Act - Update the section
      await repo.update(sectionId, {
        name: 'Updated Section Name',
        updatedAt: new Date().toISOString(),
      })

      // Assert - Should get updated value, not cached
      const updated = await repo.getById(sectionId)
      expect(updated?.name).toBe('Updated Section Name')
    })
  })
})