import { afterEach, beforeAll, beforeEach, describe, expect, it } from 'vitest'
import { createTestDb, TestDb } from '../utils/testDbFactory'
import { pollingStations, sections, voters } from '@/db/schema'
import { VoterTurnoutRepository } from '@/repositories/voterTurnoutRepository'

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('VoterTurnoutRepository (Task 2.5)', () => {
  let t: TestDb
  let repo: VoterTurnoutRepository
  let stationId: string
  let sectionId: string
  let voter1Id: string
  let voter2Id: string
  let voter3Id: string

  beforeAll(() => {
    t = createTestDb()
  })

  beforeEach(async () => {
    // Clean all tables between tests
    t.sqlite.exec('DELETE FROM voter_turnout;')
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')

    repo = new VoterTurnoutRepository(t.db)

    // Set up test data - polling station, section, and voters
    stationId = uuid()
    sectionId = uuid()
    voter1Id = uuid()
    voter2Id = uuid()
    voter3Id = uuid()
    const now = new Date().toISOString()

    await t.db.insert(pollingStations).values({
      id: stationId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      name: 'Test Station',
      code: 'TS001',
    })

    await t.db.insert(sections).values({
      id: sectionId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      pollingStationId: stationId,
      name: 'Test Section',
      code: 'SEC001',
    })

    // Create test voters
    const testVoters = [
      {
        id: voter1Id,
        name: 'Voter One',
        epicNumber: 'EPIC001',
      },
      {
        id: voter2Id,
        name: 'Voter Two',
        epicNumber: 'EPIC002',
      },
      {
        id: voter3Id,
        name: 'Voter Three',
        epicNumber: 'EPIC003',
      },
    ]

    for (const voter of testVoters) {
      await t.db.insert(voters).values({
        id: voter.id,
        createdAt: now,
        updatedAt: now,
        deletedAt: null,
        syncStatus: 'synced',
        lastSyncAt: null,
        pollingStationId: stationId,
        sectionId: sectionId,
        name: voter.name,
        epicNumber: voter.epicNumber,
        houseNumber: '1A',
        birthYear: 1990,
        gender: 'Male',
        relationshipType: 'Self',
        relationshipName: null,
        status: 'Active',
        phone: null,
        email: null,
        facebook: null,
        instagram: null,
        twitter: null,
        supporterStatus: null,
        education: null,
        occupation: null,
        community: null,
        religion: null,
        economicStatus: null,
        customNotes: null,
      })
    }
  })

  afterEach(() => {
    // Clean up after each test
    t.sqlite.exec('DELETE FROM voter_turnout;')
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')
  })

  describe('CRUD Operations', () => {
    it('should create a voter turnout record', async () => {
      // Arrange
      const turnoutId = uuid()
      const now = new Date().toISOString()
      const turnoutData = {
        id: turnoutId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter1Id,
        electionYear: 2024,
        voted: true,
      }

      // Act
      const created = await repo.create(turnoutData)

      // Assert
      expect(created.id).toBe(turnoutId)
      expect(created.voterId).toBe(voter1Id)
      expect(created.electionYear).toBe(2024)
      expect(created.voted).toBe(true)
    })

    it('should retrieve turnout record by ID', async () => {
      // Arrange
      const turnoutId = uuid()
      const now = new Date().toISOString()
      const turnoutData = {
        id: turnoutId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter2Id,
        electionYear: 2024,
        voted: false,
      }

      await repo.create(turnoutData)

      // Act
      const retrieved = await repo.getById(turnoutId)

      // Assert
      expect(retrieved).not.toBeNull()
      expect(retrieved?.voterId).toBe(voter2Id)
      expect(retrieved?.voted).toBe(false)
    })

    it('should update turnout record', async () => {
      // Arrange
      const turnoutId = uuid()
      const now = new Date().toISOString()
      const turnoutData = {
        id: turnoutId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter3Id,
        electionYear: 2024,
        voted: false,
      }

      await repo.create(turnoutData)

      // Act
      const updateData = {
        voted: true,
        updatedAt: new Date().toISOString(),
      }
      const updated = await repo.update(turnoutId, updateData)

      // Assert
      expect(updated).toBe(true)
      const retrieved = await repo.getById(turnoutId)
      expect(retrieved?.voted).toBe(true)
    })

    it('should soft delete turnout record', async () => {
      // Arrange
      const turnoutId = uuid()
      const now = new Date().toISOString()
      const turnoutData = {
        id: turnoutId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter1Id,
        electionYear: 2024,
        voted: true,
      }

      await repo.create(turnoutData)

      // Act
      const deleted = await repo.softDelete(turnoutId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)
      const retrieved = await repo.getById(turnoutId)
      expect(retrieved).toBeNull()
    })

    it('should enforce unique voter-election constraint', async () => {
      // Arrange
      const now = new Date().toISOString()
      const turnout1Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter1Id,
        electionYear: 2024,
        voted: true,
      }

      const turnout2Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter1Id, // Same voter
        electionYear: 2024, // Same election year
        voted: false,
      }

      await repo.create(turnout1Data)

      // Act & Assert
      await expect(repo.create(turnout2Data)).rejects.toBeTruthy()
    })

    it('should allow same voter in different election years', async () => {
      // Arrange
      const now = new Date().toISOString()
      const turnout2024Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter1Id,
        electionYear: 2024,
        voted: true,
      }

      const turnout2029Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voter1Id, // Same voter
        electionYear: 2029, // Different election year
        voted: false,
      }

      // Act & Assert - Should not throw
      await repo.create(turnout2024Data)
      await repo.create(turnout2029Data)

      const turnout2024 = await repo.getById(turnout2024Data.id)
      const turnout2029 = await repo.getById(turnout2029Data.id)

      expect(turnout2024?.voted).toBe(true)
      expect(turnout2029?.voted).toBe(false)
    })
  })

  describe('Turnout Tracking', () => {
    beforeEach(async () => {
      // Create test turnout records
      const now = new Date().toISOString()
      const testTurnouts = [
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          voterId: voter1Id,
          electionYear: 2024,
          voted: true,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          voterId: voter2Id,
          electionYear: 2024,
          voted: false,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          voterId: voter3Id,
          electionYear: 2024,
          voted: true,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          voterId: voter1Id,
          electionYear: 2019,
          voted: false,
        },
      ]

      for (const turnout of testTurnouts) {
        await repo.create(turnout)
      }
    })

    it('should get turnout records by voter', async () => {
      // Act
      const voterTurnouts = await repo.findByVoter(voter1Id)

      // Assert
      expect(voterTurnouts.length).toBe(2)
      voterTurnouts.forEach(turnout => {
        expect(turnout.voterId).toBe(voter1Id)
      })
    })

    it('should get turnout records by election year', async () => {
      // Act
      const election2024Turnouts = await repo.findByElectionYear(2024)

      // Assert
      expect(election2024Turnouts.length).toBe(3)
      election2024Turnouts.forEach(turnout => {
        expect(turnout.electionYear).toBe(2024)
      })
    })

    it('should get specific voter turnout for election', async () => {
      // Act
      const voterTurnout = await repo.findByVoterAndElection(voter2Id, 2024)

      // Assert
      expect(voterTurnout).not.toBeNull()
      expect(voterTurnout?.voterId).toBe(voter2Id)
      expect(voterTurnout?.electionYear).toBe(2024)
      expect(voterTurnout?.voted).toBe(false)
    })

    it('should get voter voting history', async () => {
      // Act
      const history = await repo.getVotingHistory(voter1Id)

      // Assert
      expect(history.length).toBe(2)
      // Should be ordered by election year descending
      expect(history[0].electionYear).toBe(2024)
      expect(history[1].electionYear).toBe(2019)
    })
  })

  describe('Statistical Operations', () => {
    beforeEach(async () => {
      // Create comprehensive test data for statistics
      const now = new Date().toISOString()
      const testTurnouts = [
        // 2024 Election - 2 voted, 1 didn't vote
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter1Id, electionYear: 2024, voted: true },
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter2Id, electionYear: 2024, voted: false },
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter3Id, electionYear: 2024, voted: true },
        // 2019 Election - 1 voted, 2 didn't vote
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter1Id, electionYear: 2019, voted: false },
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter2Id, electionYear: 2019, voted: true },
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter3Id, electionYear: 2019, voted: false },
      ]

      for (const turnout of testTurnouts) {
        await repo.create(turnout)
      }
    })

    it('should calculate turnout percentage for election year', async () => {
      // Act
      const turnout2024 = await repo.getTurnoutPercentage(2024)
      const turnout2019 = await repo.getTurnoutPercentage(2019)

      // Assert
      expect(turnout2024).toBeCloseTo(66.67, 2) // 2 out of 3 voted
      expect(turnout2019).toBeCloseTo(33.33, 2) // 1 out of 3 voted
    })

    it('should get turnout statistics for election year', async () => {
      // Act
      const stats2024 = await repo.getTurnoutStatistics(2024)

      // Assert
      expect(stats2024.totalVoters).toBe(3)
      expect(stats2024.votedCount).toBe(2)
      expect(stats2024.notVotedCount).toBe(1)
      expect(stats2024.turnoutPercentage).toBeCloseTo(66.67, 2)
    })

    it('should get turnout comparison between elections', async () => {
      // Act
      const comparison = await repo.getTurnoutComparison([2019, 2024])

      // Assert
      expect(comparison.length).toBe(2)

      const stats2019 = comparison.find(c => c.electionYear === 2019)
      const stats2024 = comparison.find(c => c.electionYear === 2024)

      expect(stats2019?.turnoutPercentage).toBeCloseTo(33.33, 2)
      expect(stats2024?.turnoutPercentage).toBeCloseTo(66.67, 2)
    })

    it('should get voter participation summary', async () => {
      // Act
      const summary = await repo.getVoterParticipationSummary(voter1Id)

      // Assert
      expect(summary.totalElections).toBe(2)
      expect(summary.electionsVoted).toBe(1) // Voted in 2024, not in 2019
      expect(summary.participationRate).toBeCloseTo(50.0, 2)
    })
  })

  describe('Batch Operations', () => {
    it('should perform bulk turnout updates', async () => {
      // Arrange
      const now = new Date().toISOString()
      const bulkTurnouts = [
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'pending',
          voterId: voter1Id,
          electionYear: 2024,
          voted: true,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'pending',
          voterId: voter2Id,
          electionYear: 2024,
          voted: false,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'pending',
          voterId: voter3Id,
          electionYear: 2024,
          voted: true,
        },
      ]

      // Act
      const created = await repo.bulkCreate(bulkTurnouts)

      // Assert
      expect(created).toBe(3)

      // Verify all records were created
      const allTurnouts = await repo.findByElectionYear(2024)
      expect(allTurnouts.length).toBe(3)
    })

    it('should update multiple turnout records for election', async () => {
      // Arrange - Create initial records
      const now = new Date().toISOString()
      const initialTurnouts = [
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'pending',
          voterId: voter1Id,
          electionYear: 2024,
          voted: false,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'pending',
          voterId: voter2Id,
          electionYear: 2024,
          voted: false,
        },
      ]

      for (const turnout of initialTurnouts) {
        await repo.create(turnout)
      }

      // Act - Mark voters as having voted
      const voterIds = [voter1Id, voter2Id]
      const updated = await repo.markVotersAsVoted(voterIds, 2024)

      // Assert
      expect(updated).toBe(2)

      // Verify updates
      const voter1Turnout = await repo.findByVoterAndElection(voter1Id, 2024)
      const voter2Turnout = await repo.findByVoterAndElection(voter2Id, 2024)

      expect(voter1Turnout?.voted).toBe(true)
      expect(voter2Turnout?.voted).toBe(true)
    })
  })

  describe('Filtering and Pagination', () => {
    beforeEach(async () => {
      // Create test data for filtering
      const now = new Date().toISOString()
      const testTurnouts = [
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter1Id, electionYear: 2024, voted: true },
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'pending', voterId: voter2Id, electionYear: 2024, voted: false },
        { id: uuid(), createdAt: now, updatedAt: now, syncStatus: 'synced', voterId: voter3Id, electionYear: 2019, voted: true },
      ]

      for (const turnout of testTurnouts) {
        await repo.create(turnout)
      }
    })

    it('should filter turnout records by sync status', async () => {
      // Act
      const syncedTurnouts = await repo.findMany({ syncStatus: 'synced' })

      // Assert
      expect(syncedTurnouts.length).toBe(2)
      syncedTurnouts.forEach(turnout => {
        expect(turnout.syncStatus).toBe('synced')
      })
    })

    it('should filter turnout records by voted status', async () => {
      // Act
      const votedTurnouts = await repo.findMany({ voted: true })

      // Assert
      expect(votedTurnouts.length).toBe(2)
      votedTurnouts.forEach(turnout => {
        expect(turnout.voted).toBe(true)
      })
    })

    it('should support pagination', async () => {
      // Act
      const page1 = await repo.findMany({ limit: 2, offset: 0 })
      const page2 = await repo.findMany({ limit: 2, offset: 2 })

      // Assert
      expect(page1.length).toBe(2)
      expect(page2.length).toBe(1)

      // Ensure no duplicates between pages
      const page1Ids = page1.map(t => t.id)
      const page2Ids = page2.map(t => t.id)
      const intersection = page1Ids.filter(id => page2Ids.includes(id))
      expect(intersection.length).toBe(0)
    })

    it('should combine multiple filters', async () => {
      // Act
      const filteredTurnouts = await repo.findMany({
        electionYear: 2024,
        voted: true
      })

      // Assert
      expect(filteredTurnouts.length).toBe(1)
      expect(filteredTurnouts[0].electionYear).toBe(2024)
      expect(filteredTurnouts[0].voted).toBe(true)
    })
  })

  describe('Caching', () => {
    it('should cache frequently accessed turnout records', async () => {
      // Arrange
      const turnoutId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: turnoutId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        voterId: voter1Id,
        electionYear: 2024,
        voted: true,
      })

      // Act - Access same record multiple times
      const firstCall = await repo.getById(turnoutId)
      const secondCall = await repo.getById(turnoutId)

      // Assert
      expect(firstCall).toEqual(secondCall)
      expect(firstCall?.voted).toBe(true)
    })

    it('should invalidate cache on updates', async () => {
      // Arrange
      const turnoutId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: turnoutId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        voterId: voter1Id,
        electionYear: 2024,
        voted: false,
      })

      // Get initial cached value
      const initial = await repo.getById(turnoutId)
      expect(initial?.voted).toBe(false)

      // Act - Update the record
      await repo.update(turnoutId, {
        voted: true,
        updatedAt: new Date().toISOString(),
      })

      // Assert - Should get updated value, not cached
      const updated = await repo.getById(turnoutId)
      expect(updated?.voted).toBe(true)
    })
  })
})