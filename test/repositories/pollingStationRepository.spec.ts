import { afterEach, beforeAll, beforeEach, describe, expect, it } from 'vitest'
import { createTestDb, TestDb } from '../utils/testDbFactory'
import { sections, voters } from '@/db/schema'
import { PollingStationRepository } from '@/repositories/pollingStationRepository'
import { eq } from 'drizzle-orm'

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('PollingStationRepository (Task 2.4)', () => {
  let t: TestDb
  let repo: PollingStationRepository

  beforeAll(() => {
    t = createTestDb()
  })

  beforeEach(() => {
    // Clean all tables between tests
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')

    repo = new PollingStationRepository(t.db)
  })

  afterEach(() => {
    // Clean up after each test
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')
  })

  describe('CRUD Operations', () => {
    it('should create a polling station with all required fields', async () => {
      // Arrange
      const stationId = uuid()
      const now = new Date().toISOString()
      const stationData = {
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        name: 'Central Polling Station',
        code: 'CPS001',
      }

      // Act
      const created = await repo.create(stationData)

      // Assert
      expect(created.id).toBe(stationId)
      expect(created.name).toBe('Central Polling Station')
      expect(created.code).toBe('CPS001')
    })

    it('should retrieve polling station by ID', async () => {
      // Arrange
      const stationId = uuid()
      const now = new Date().toISOString()
      const stationData = {
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        name: 'North Polling Station',
        code: 'NPS001',
      }

      await repo.create(stationData)

      // Act
      const retrieved = await repo.getById(stationId)

      // Assert
      expect(retrieved).not.toBeNull()
      expect(retrieved?.name).toBe('North Polling Station')
      expect(retrieved?.code).toBe('NPS001')
    })

    it('should update polling station information', async () => {
      // Arrange
      const stationId = uuid()
      const now = new Date().toISOString()
      const stationData = {
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        name: 'South Polling Station',
        code: 'SPS001',
      }

      await repo.create(stationData)

      // Act
      const updateData = {
        name: 'South Central Polling Station',
        updatedAt: new Date().toISOString(),
      }
      const updated = await repo.update(stationId, updateData)

      // Assert
      expect(updated).toBe(true)
      const retrieved = await repo.getById(stationId)
      expect(retrieved?.name).toBe('South Central Polling Station')
    })

    it('should soft delete polling station', async () => {
      // Arrange
      const stationId = uuid()
      const now = new Date().toISOString()
      const stationData = {
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        name: 'East Polling Station',
        code: 'EPS001',
      }

      await repo.create(stationData)

      // Act
      const deleted = await repo.softDelete(stationId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)
      const retrieved = await repo.getById(stationId)
      expect(retrieved).toBeNull()
    })

    it('should enforce unique code constraint', async () => {
      // Arrange
      const now = new Date().toISOString()
      const code = 'UNIQUE001'

      const station1Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        name: 'First Station',
        code: code,
      }

      const station2Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        name: 'Second Station',
        code: code, // Same code
      }

      await repo.create(station1Data)

      // Act & Assert
      await expect(repo.create(station2Data)).rejects.toBeTruthy()
    })
  })

  describe('Hierarchical Relationships', () => {
    let stationId: string

    beforeEach(async () => {
      // Create a polling station for relationship tests
      stationId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        name: 'Test Station',
        code: 'TS001',
      })
    })

    it('should get polling station with its sections', async () => {
      // Arrange - Create sections for the station
      const now = new Date().toISOString()
      const section1Id = uuid()
      const section2Id = uuid()

      await t.db.insert(sections).values([
        {
          id: section1Id,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          name: 'Section A',
          code: 'SEC-A',
        },
        {
          id: section2Id,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          name: 'Section B',
          code: 'SEC-B',
        },
      ])

      // Act
      const stationWithSections = await repo.getWithSections(stationId)

      // Assert
      expect(stationWithSections).not.toBeNull()
      expect(stationWithSections?.sections).toHaveLength(2)
      expect(stationWithSections?.sections.map(s => s.name)).toContain('Section A')
      expect(stationWithSections?.sections.map(s => s.name)).toContain('Section B')
    })

    it('should get polling station with voter count', async () => {
      // Arrange - Create section and voters
      const now = new Date().toISOString()
      const sectionId = uuid()

      await t.db.insert(sections).values({
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        deletedAt: null,
        syncStatus: 'synced',
        lastSyncAt: null,
        pollingStationId: stationId,
        name: 'Section A',
        code: 'SEC-A',
      })

      // Create some voters
      await t.db.insert(voters).values([
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Voter 1',
          epicNumber: 'EPIC001',
          houseNumber: '1A',
          birthYear: 1990,
          gender: 'Male',
          relationshipType: 'Self',
          relationshipName: null,
          status: 'Active',
          phone: null,
          email: null,
          facebook: null,
          instagram: null,
          twitter: null,
          supporterStatus: null,
          education: null,
          occupation: null,
          community: null,
          religion: null,
          economicStatus: null,
          customNotes: null,
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Voter 2',
          epicNumber: 'EPIC002',
          houseNumber: '2B',
          birthYear: 1985,
          gender: 'Female',
          relationshipType: 'Self',
          relationshipName: null,
          status: 'Active',
          phone: null,
          email: null,
          facebook: null,
          instagram: null,
          twitter: null,
          supporterStatus: null,
          education: null,
          occupation: null,
          community: null,
          religion: null,
          economicStatus: null,
          customNotes: null,
        },
      ])

      // Act
      const stationWithCount = await repo.getWithVoterCount(stationId)

      // Assert
      expect(stationWithCount).not.toBeNull()
      expect(stationWithCount?.voterCount).toBe(2)
    })
  })

  describe('Cascade Operations', () => {
    let stationId: string

    beforeEach(async () => {
      // Create a polling station for cascade tests
      stationId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        name: 'Cascade Test Station',
        code: 'CTS001',
      })
    })

    it('should prevent deletion when sections exist', async () => {
      // Arrange - Create a section for the station
      const now = new Date().toISOString()
      const sectionId = uuid()

      await t.db.insert(sections).values({
        id: sectionId,
        createdAt: now,
        updatedAt: now,
        deletedAt: null,
        syncStatus: 'synced',
        lastSyncAt: null,
        pollingStationId: stationId,
        name: 'Section A',
        code: 'SEC-A',
      })

      // Act & Assert
      await expect(repo.softDelete(stationId, new Date().toISOString())).rejects.toBeTruthy()
    })

    it('should allow deletion when no active sections exist', async () => {
      // Act
      const deleted = await repo.softDelete(stationId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)
      const retrieved = await repo.getById(stationId)
      expect(retrieved).toBeNull()
    })

    it('should cascade soft delete to sections when forced', async () => {
      // Arrange - Create sections for the station
      const now = new Date().toISOString()
      const section1Id = uuid()
      const section2Id = uuid()

      await t.db.insert(sections).values([
        {
          id: section1Id,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          name: 'Section A',
          code: 'SEC-A',
        },
        {
          id: section2Id,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
          syncStatus: 'synced',
          lastSyncAt: null,
          pollingStationId: stationId,
          name: 'Section B',
          code: 'SEC-B',
        },
      ])

      // Act
      const deleted = await repo.cascadeDelete(stationId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)

      // Verify station is deleted
      const retrievedStation = await repo.getById(stationId)
      expect(retrievedStation).toBeNull()

      // Verify sections are also soft deleted
      const sectionsResult = await t.db
        .select()
        .from(sections)
        .where(eq(sections.pollingStationId, stationId))

      sectionsResult.forEach(section => {
        expect(section.deletedAt).not.toBeNull()
      })
    })
  })

  describe('Filtering and Search', () => {
    beforeEach(async () => {
      // Create test polling stations
      const now = new Date().toISOString()
      const stations = [
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          name: 'Central Station',
          code: 'CS001',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'pending',
          name: 'North Station',
          code: 'NS001',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          name: 'South Station',
          code: 'SS001',
        },
      ]

      for (const station of stations) {
        await repo.create(station)
      }
    })

    it('should find stations by sync status', async () => {
      // Act
      const syncedStations = await repo.findMany({ syncStatus: 'synced' })

      // Assert
      expect(syncedStations.length).toBe(2)
      syncedStations.forEach(station => {
        expect(station.syncStatus).toBe('synced')
      })
    })

    it('should search stations by name', async () => {
      // Act
      const results = await repo.searchByName('Central')

      // Assert
      expect(results.length).toBe(1)
      expect(results[0].name).toBe('Central Station')
    })

    it('should find station by code', async () => {
      // Act
      const station = await repo.findByCode('NS001')

      // Assert
      expect(station).not.toBeNull()
      expect(station?.name).toBe('North Station')
    })

    it('should support pagination', async () => {
      // Act
      const page1 = await repo.findMany({ limit: 2, offset: 0 })
      const page2 = await repo.findMany({ limit: 2, offset: 2 })

      // Assert
      expect(page1.length).toBe(2)
      expect(page2.length).toBe(1)

      // Ensure no duplicates between pages
      const page1Ids = page1.map(s => s.id)
      const page2Ids = page2.map(s => s.id)
      const intersection = page1Ids.filter(id => page2Ids.includes(id))
      expect(intersection.length).toBe(0)
    })
  })

  describe('Caching', () => {
    it('should cache frequently accessed stations', async () => {
      // Arrange
      const stationId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        name: 'Cached Station',
        code: 'CACHE001',
      })

      // Act - Access same station multiple times
      const firstCall = await repo.getById(stationId)
      const secondCall = await repo.getById(stationId)

      // Assert
      expect(firstCall).toEqual(secondCall)
      expect(firstCall?.name).toBe('Cached Station')
    })

    it('should invalidate cache on updates', async () => {
      // Arrange
      const stationId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: stationId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        name: 'Original Name',
        code: 'ORIG001',
      })

      // Get initial cached value
      const initial = await repo.getById(stationId)
      expect(initial?.name).toBe('Original Name')

      // Act - Update the station
      await repo.update(stationId, {
        name: 'Updated Name',
        updatedAt: new Date().toISOString(),
      })

      // Assert - Should get updated value, not cached
      const updated = await repo.getById(stationId)
      expect(updated?.name).toBe('Updated Name')
    })
  })
})