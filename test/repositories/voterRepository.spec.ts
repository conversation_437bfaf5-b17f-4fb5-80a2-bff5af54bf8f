import { afterEach, beforeAll, beforeEach, describe, expect, it } from 'vitest'
import { createTestDb, TestDb } from '../utils/testDbFactory'
import { pollingStations, sections } from '@/db/schema'
import { VoterRepository } from '@/repositories/voterRepository'

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('VoterRepository (Task 2.3)', () => {
  let t: TestDb
  let repo: VoterRepository
  let stationId: string
  let sectionId: string

  beforeAll(() => {
    t = createTestDb()
  })

  beforeEach(async () => {
    // Clean all tables between tests
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')

    repo = new VoterRepository(t.db)

    // Set up test data - polling station and section
    stationId = uuid()
    sectionId = uuid()
    const now = new Date().toISOString()

    await t.db.insert(pollingStations).values({
      id: stationId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      name: 'Test Station',
      code: 'TS001',
    })

    await t.db.insert(sections).values({
      id: sectionId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      pollingStationId: stationId,
      name: 'Test Section',
      code: 'SEC001',
    })
  })

  afterEach(() => {
    // Clean up after each test
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')
  })

  describe('CRUD Operations', () => {
    it('should create a voter with all required fields', async () => {
      // Arrange
      const voterId = uuid()
      const now = new Date().toISOString()
      const voterData = {
        id: voterId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'John Doe',
        epicNumber: '**********',
        houseNumber: '123A',
        birthYear: 1990,
        gender: 'Male',
        relationshipType: 'Self',
        status: 'Active',
      }

      // Act
      const created = await repo.create(voterData)

      // Assert
      expect(created.id).toBe(voterId)
      expect(created.name).toBe('John Doe')
      expect(created.epicNumber).toBe('**********')
    })

    it('should retrieve voter by ID', async () => {
      // Arrange
      const voterId = uuid()
      const now = new Date().toISOString()
      const voterData = {
        id: voterId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'Jane Smith',
        epicNumber: '**********',
        houseNumber: '456B',
        birthYear: 1985,
        gender: 'Female',
        relationshipType: 'Self',
        status: 'Active',
      }

      await repo.create(voterData)

      // Act
      const retrieved = await repo.getById(voterId)

      // Assert
      expect(retrieved).not.toBeNull()
      expect(retrieved?.name).toBe('Jane Smith')
      expect(retrieved?.epicNumber).toBe('**********')
    })

    it('should update voter information', async () => {
      // Arrange
      const voterId = uuid()
      const now = new Date().toISOString()
      const voterData = {
        id: voterId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'Bob Wilson',
        epicNumber: '**********',
        houseNumber: '789C',
        birthYear: 1975,
        gender: 'Male',
        relationshipType: 'Self',
        status: 'Active',
      }

      await repo.create(voterData)

      // Act
      const updateData = {
        name: 'Robert Wilson',
        phone: '9876543210',
        updatedAt: new Date().toISOString(),
      }
      const updated = await repo.update(voterId, updateData)

      // Assert
      expect(updated).toBe(true)
      const retrieved = await repo.getById(voterId)
      expect(retrieved?.name).toBe('Robert Wilson')
      expect(retrieved?.phone).toBe('9876543210')
    })

    it('should soft delete voter', async () => {
      // Arrange
      const voterId = uuid()
      const now = new Date().toISOString()
      const voterData = {
        id: voterId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'Alice Brown',
        epicNumber: '**********',
        houseNumber: '321D',
        birthYear: 1995,
        gender: 'Female',
        relationshipType: 'Self',
        status: 'Active',
      }

      await repo.create(voterData)

      // Act
      const deleted = await repo.softDelete(voterId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)
      const retrieved = await repo.getById(voterId)
      expect(retrieved).toBeNull()
    })

    it('should enforce unique EPIC number constraint', async () => {
      // Arrange
      const now = new Date().toISOString()
      const epicNumber = 'UNIQUE123456'

      const voter1Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'First Voter',
        epicNumber: epicNumber,
        houseNumber: '111A',
        birthYear: 1990,
        gender: 'Male',
        relationshipType: 'Self',
        status: 'Active',
      }

      const voter2Data = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'Second Voter',
        epicNumber: epicNumber, // Same EPIC number
        houseNumber: '222B',
        birthYear: 1985,
        gender: 'Female',
        relationshipType: 'Self',
        status: 'Active',
      }

      await repo.create(voter1Data)

      // Act & Assert
      await expect(repo.create(voter2Data)).rejects.toBeTruthy()
    })
  })

  describe('FTS5 Search Functionality', () => {
    beforeEach(async () => {
      // Insert test voters for search
      const now = new Date().toISOString()
      const testVoters = [
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'John Doe',
          epicNumber: '**********',
          houseNumber: '123A',
          birthYear: 1990,
          gender: 'Male',
          relationshipType: 'Self',
          status: 'Active',
          customNotes: 'Software engineer, tech enthusiast',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Jane Smith',
          epicNumber: '**********',
          houseNumber: '456B',
          birthYear: 1985,
          gender: 'Female',
          relationshipType: 'Self',
          status: 'Active',
          customNotes: 'Teacher, loves reading books',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Bob Johnson',
          epicNumber: '**********',
          houseNumber: '789C',
          birthYear: 1975,
          gender: 'Male',
          relationshipType: 'Self',
          status: 'Active',
          customNotes: 'Doctor, medical professional',
        },
      ]

      for (const voter of testVoters) {
        await repo.create(voter)
      }
    })

    it('should search voters by name using FTS5', async () => {
      // Act
      const results = await repo.searchByText('John')

      // Assert
      expect(results.length).toBe(1)
      expect(results[0].name).toBe('John Doe')
    })

    it('should search voters by EPIC number using FTS5', async () => {
      // Act
      const results = await repo.searchByText('**********')

      // Assert
      expect(results.length).toBe(1)
      expect(results[0].name).toBe('Jane Smith')
    })

    it('should search voters by house number using FTS5', async () => {
      // Act
      const results = await repo.searchByText('789C')

      // Assert
      expect(results.length).toBe(1)
      expect(results[0].name).toBe('Bob Johnson')
    })

    it('should search voters by custom notes using FTS5', async () => {
      // Act
      const results = await repo.searchByText('engineer')

      // Assert
      expect(results.length).toBe(1)
      expect(results[0].name).toBe('John Doe')
    })

    it('should return multiple results for partial matches', async () => {
      // Act - Search for 'Jo' which should match John and Johnson
      const results = await repo.searchByText('Jo*')

      // Assert
      expect(results.length).toBeGreaterThan(0)
      const names = results.map(r => r.name)
      expect(names).toContain('John Doe')
      expect(names).toContain('Bob Johnson')
    })

    it('should return empty array for no matches', async () => {
      // Act
      const results = await repo.searchByText('nonexistent')

      // Assert
      expect(results).toEqual([])
    })
  })

  describe('Filtering and Pagination', () => {
    beforeEach(async () => {
      // Insert test voters with different attributes
      const now = new Date().toISOString()
      const testVoters = [
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Alice Active',
          epicNumber: 'ACTIVE001',
          houseNumber: '100A',
          birthYear: 1990,
          gender: 'Female',
          relationshipType: 'Self',
          status: 'Active',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Bob Expired',
          epicNumber: 'EXPIRED001',
          houseNumber: '200B',
          birthYear: 1980,
          gender: 'Male',
          relationshipType: 'Self',
          status: 'Expired',
        },
        {
          id: uuid(),
          createdAt: now,
          updatedAt: now,
          syncStatus: 'synced',
          pollingStationId: stationId,
          sectionId: sectionId,
          name: 'Carol Shifted',
          epicNumber: 'SHIFTED001',
          houseNumber: '300C',
          birthYear: 1985,
          gender: 'Female',
          relationshipType: 'Self',
          status: 'Shifted',
        },
      ]

      for (const voter of testVoters) {
        await repo.create(voter)
      }
    })

    it('should filter voters by status', async () => {
      // Act
      const activeVoters = await repo.findMany({ status: 'Active' })

      // Assert
      expect(activeVoters.length).toBe(1)
      expect(activeVoters[0].name).toBe('Alice Active')
    })

    it('should filter voters by gender', async () => {
      // Act
      const femaleVoters = await repo.findMany({ gender: 'Female' })

      // Assert
      expect(femaleVoters.length).toBe(2)
      const names = femaleVoters.map(v => v.name)
      expect(names).toContain('Alice Active')
      expect(names).toContain('Carol Shifted')
    })

    it('should filter voters by section', async () => {
      // Act
      const sectionVoters = await repo.findMany({ sectionId: sectionId })

      // Assert
      expect(sectionVoters.length).toBe(3) // All test voters are in the same section
    })

    it('should support pagination with limit and offset', async () => {
      // Act
      const page1 = await repo.findMany({ limit: 2, offset: 0 })
      const page2 = await repo.findMany({ limit: 2, offset: 2 })

      // Assert
      expect(page1.length).toBe(2)
      expect(page2.length).toBe(1)

      // Ensure no duplicates between pages
      const page1Ids = page1.map(v => v.id)
      const page2Ids = page2.map(v => v.id)
      const intersection = page1Ids.filter(id => page2Ids.includes(id))
      expect(intersection.length).toBe(0)
    })

    it('should combine multiple filters', async () => {
      // Act
      const filteredVoters = await repo.findMany({
        gender: 'Female',
        status: 'Active'
      })

      // Assert
      expect(filteredVoters.length).toBe(1)
      expect(filteredVoters[0].name).toBe('Alice Active')
    })
  })

  describe('Duplicate Detection', () => {
    it('should detect duplicate by EPIC number', async () => {
      // Arrange
      const epicNumber = 'DUPLICATE123'
      const now = new Date().toISOString()

      const existingVoter = {
        id: uuid(),
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        pollingStationId: stationId,
        sectionId: sectionId,
        name: 'Existing Voter',
        epicNumber: epicNumber,
        houseNumber: '100A',
        birthYear: 1990,
        gender: 'Male',
        relationshipType: 'Self',
        status: 'Active',
      }

      await repo.create(existingVoter)

      // Act
      const duplicate = await repo.findByEpicNumber(epicNumber)

      // Assert
      expect(duplicate).not.toBeNull()
      expect(duplicate?.name).toBe('Existing Voter')
    })

    it('should return null for non-existent EPIC number', async () => {
      // Act
      const result = await repo.findByEpicNumber('NONEXISTENT123')

      // Assert
      expect(result).toBeNull()
    })
  })
})