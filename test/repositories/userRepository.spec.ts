import { beforeAll, beforeEach, describe, expect, it } from 'vitest'
import { createTestDb, TestDb } from '../utils/testDbFactory'
import { eq } from 'drizzle-orm'
import { users } from '@/db/schema'
import { UserRepository } from '@/repositories/userRepository'

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('UserRepository (Task 2.1)', () => {
  let t: TestDb
  let repo: UserRepository

  beforeAll(() => {
    t = createTestDb()
  })

  beforeEach(() => {
    // clean users table between tests
    t.sqlite.exec('DELETE FROM users;')
    repo = new UserRepository(t.db)
  })

  it('creates a user with hashed password and retrieves by username', async () => {
    const id = uuid()
    const now = new Date().toISOString()
    const created = await repo.create({
      id,
      username: 'alice',
      password: 'S3cret!pass',
      role: 'admin',
      isActive: true,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
    })

    expect(created.id).toBe(id)
    expect(created.username).toBe('alice')
    // password should not be returned
    // ensure stored hash present
    const row = (await t.db.select().from(users).where(eq(users.id, id))).at(0)!
    expect(row.passwordHash).toMatch(/\$/)

    const byUsername = await repo.getByUsername('alice')
    expect(byUsername?.id).toBe(id)
  })

  it('enforces unique username', async () => {
    const now = new Date().toISOString()
    await repo.create({
      id: uuid(),
      username: 'bob',
      password: 'pw1_long',
      role: 'user',
      isActive: true,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
    })

    await expect(
      repo.create({
        id: uuid(),
        username: 'bob',
        password: 'pw2_long',
        role: 'user',
        isActive: true,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
      }),
    ).rejects.toBeTruthy()
  })

  it('authenticates with correct password, updates lastLoginAt, and blocks inactive users', async () => {
    const now = new Date().toISOString()
    await repo.create({
      id: uuid(),
      username: 'carol',
      password: 'right-pass',
      role: 'user',
      isActive: true,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
    })

    const authOk = await repo.authenticate('carol', 'right-pass')
    expect(authOk?.username).toBe('carol')

    const stored = (await t.db.select().from(users).where(eq(users.username, 'carol'))).at(0)!
    expect(stored.lastLoginAt).toBeTruthy()

    // make inactive and ensure auth fails
    await repo.update(stored.id, { isActive: false, updatedAt: new Date().toISOString() })
    const authFail = await repo.authenticate('carol', 'right-pass')
    expect(authFail).toBeNull()
  })

  it('updates role and isActive, respects soft delete', async () => {
    const now = new Date().toISOString()
    const u = await repo.create({
      id: uuid(),
      username: 'dave',
      password: 'pass_long',
      role: 'user',
      isActive: true,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
    })

    await repo.update(u.id, { role: 'manager', isActive: false, updatedAt: new Date().toISOString() })
    const got = await repo.getById(u.id)
    expect(got?.role).toBe('manager')
    expect(got?.isActive).toBe(false)

    await repo.softDelete(u.id, new Date().toISOString())
    const afterDelete = await repo.getById(u.id)
    expect(afterDelete).toBeNull()
  })

  it('caches getByUsername and invalidates on mutation', async () => {
    const now = new Date().toISOString()
    await repo.create({
      id: uuid(),
      username: 'erin',
      password: 'pw_long',
      role: 'user',
      isActive: true,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'pending',
    })

    const a = await repo.getByUsername('erin')
    const b = await repo.getByUsername('erin')
    expect(a && b && a.id === b.id).toBe(true)

    // invalidate via update
    await repo.update(a!.id, { role: 'admin', updatedAt: new Date().toISOString() })
    const c = await repo.getByUsername('erin')
    expect(c?.role).toBe('admin')
  })
})
