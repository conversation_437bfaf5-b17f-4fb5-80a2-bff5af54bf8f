import { afterEach, beforeAll, beforeEach, describe, expect, it } from 'vitest'
import { createTestDb, TestDb } from '../utils/testDbFactory'
import { pollingStations, sections, voters } from '@/db/schema'
import { TransactionRepository } from '@/repositories/transactionRepository'

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('TransactionRepository (Task 2.5)', () => {
  let t: TestDb
  let repo: TransactionRepository
  let stationId: string
  let sectionId: string
  let voterId: string

  beforeAll(() => {
    t = createTestDb()
  })

  beforeEach(async () => {
    // Clean all tables between tests
    t.sqlite.exec('DELETE FROM transactions;')
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')

    repo = new TransactionRepository(t.db)

    // Set up test data - polling station, section, and voter
    stationId = uuid()
    sectionId = uuid()
    voterId = uuid()
    const now = new Date().toISOString()

    await t.db.insert(pollingStations).values({
      id: stationId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      name: 'Test Station',
      code: 'TS001',
    })

    await t.db.insert(sections).values({
      id: sectionId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      pollingStationId: stationId,
      name: 'Test Section',
      code: 'SEC001',
    })

    await t.db.insert(voters).values({
      id: voterId,
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: null,
      pollingStationId: stationId,
      sectionId: sectionId,
      name: 'Test Voter',
      epicNumber: 'EPIC001',
      houseNumber: '1A',
      birthYear: 1990,
      gender: 'Male',
      relationshipType: 'Self',
      relationshipName: null,
      status: 'Active',
      phone: null,
      email: null,
      facebook: null,
      instagram: null,
      twitter: null,
      supporterStatus: null,
      education: null,
      occupation: null,
      community: null,
      religion: null,
      economicStatus: null,
      customNotes: null,
    })
  })

  afterEach(() => {
    // Clean up after each test
    t.sqlite.exec('DELETE FROM transactions;')
    t.sqlite.exec('DELETE FROM voters;')
    t.sqlite.exec('DELETE FROM sections;')
    t.sqlite.exec('DELETE FROM polling_stations;')
  })

  describe('CRUD Operations', () => {
    it('should create a transaction with all required fields', async () => {
      // Arrange
      const transactionId = uuid()
      const now = new Date().toISOString()
      const transactionData = {
        id: transactionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voterId,
        date: '2024-01-15',
        purpose: 'Medical assistance',
        amount: '500.00',
      }

      // Act
      const created = await repo.create(transactionData)

      // Assert
      expect(created.id).toBe(transactionId)
      expect(created.voterId).toBe(voterId)
      expect(created.purpose).toBe('Medical assistance')
      expect(created.amount).toBe('500.00')
    })

    it('should retrieve transaction by ID', async () => {
      // Arrange
      const transactionId = uuid()
      const now = new Date().toISOString()
      const transactionData = {
        id: transactionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voterId,
        date: '2024-02-10',
        purpose: 'Educational support',
        amount: '1000.00',
      }

      await repo.create(transactionData)

      // Act
      const retrieved = await repo.getById(transactionId)

      // Assert
      expect(retrieved).not.toBeNull()
      expect(retrieved?.purpose).toBe('Educational support')
      expect(retrieved?.amount).toBe('1000.00')
    })

    it('should update transaction information', async () => {
      // Arrange
      const transactionId = uuid()
      const now = new Date().toISOString()
      const transactionData = {
        id: transactionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voterId,
        date: '2024-03-05',
        purpose: 'Emergency aid',
        amount: '750.00',
      }

      await repo.create(transactionData)

      // Act
      const updateData = {
        purpose: 'Emergency medical aid',
        amount: '800.00',
        updatedAt: new Date().toISOString(),
      }
      const updated = await repo.update(transactionId, updateData)

      // Assert
      expect(updated).toBe(true)
      const retrieved = await repo.getById(transactionId)
      expect(retrieved?.purpose).toBe('Emergency medical aid')
      expect(retrieved?.amount).toBe('800.00')
    })

    it('should soft delete transaction', async () => {
      // Arrange
      const transactionId = uuid()
      const now = new Date().toISOString()
      const transactionData = {
        id: transactionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'pending',
        voterId: voterId,
        date: '2024-04-01',
        purpose: 'Housing support',
        amount: '2000.00',
      }

      await repo.create(transactionData)

      // Act
      const deleted = await repo.softDelete(transactionId, new Date().toISOString())

      // Assert
      expect(deleted).toBe(true)
      const retrieved = await repo.getById(transactionId)
      expect(retrieved).toBeNull()
    })

    it('should enforce foreign key constraint with voter', async () => {
      // Arrange
      const nonExistentVoterId = uuid()
      const transactionData = {
        id: uuid(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        syncStatus: 'pending',
        voterId: nonExistentVoterId, // Non-existent voter
        date: '2024-05-01',
        purpose: 'Invalid transaction',
        amount: '100.00',
      }

      // Act & Assert
      await expect(repo.create(transactionData)).rejects.toBeTruthy()
    })
  })

  describe('Audit Trail Functionality', () => {
    beforeEach(async () => {
      // Create test transactions for audit trail
      const testTransactions = [
        {
          id: uuid(),
          createdAt: '2024-01-01T10:00:00Z',
          updatedAt: '2024-01-01T10:00:00Z',
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-01-01',
          purpose: 'Medical assistance',
          amount: '500.00',
        },
        {
          id: uuid(),
          createdAt: '2024-01-15T14:30:00Z',
          updatedAt: '2024-01-15T14:30:00Z',
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-01-15',
          purpose: 'Educational support',
          amount: '1000.00',
        },
        {
          id: uuid(),
          createdAt: '2024-02-01T09:15:00Z',
          updatedAt: '2024-02-01T09:15:00Z',
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-02-01',
          purpose: 'Emergency aid',
          amount: '750.00',
        },
      ]

      for (const transaction of testTransactions) {
        await repo.create(transaction)
      }
    })

    it('should get transactions by voter', async () => {
      // Act
      const voterTransactions = await repo.findByVoter(voterId)

      // Assert
      expect(voterTransactions.length).toBe(3)
      voterTransactions.forEach(transaction => {
        expect(transaction.voterId).toBe(voterId)
      })
    })

    it('should get transactions by date range', async () => {
      // Act
      const januaryTransactions = await repo.findByDateRange('2024-01-01', '2024-01-31')

      // Assert
      expect(januaryTransactions.length).toBe(2)
      januaryTransactions.forEach(transaction => {
        expect(transaction.date >= '2024-01-01').toBe(true)
        expect(transaction.date <= '2024-01-31').toBe(true)
      })
    })

    it('should get transactions by purpose', async () => {
      // Act
      const medicalTransactions = await repo.findByPurpose('Medical')

      // Assert
      expect(medicalTransactions.length).toBe(1)
      expect(medicalTransactions[0].purpose).toBe('Medical assistance')
    })

    it('should get transaction history with chronological order', async () => {
      // Act
      const history = await repo.getTransactionHistory(voterId)

      // Assert
      expect(history.length).toBe(3)
      // Should be ordered by date descending (most recent first)
      expect(history[0].date).toBe('2024-02-01')
      expect(history[1].date).toBe('2024-01-15')
      expect(history[2].date).toBe('2024-01-01')
    })
  })

  describe('Statistical Operations', () => {
    beforeEach(async () => {
      // Create test transactions for statistics
      const testTransactions = [
        {
          id: uuid(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-01-01',
          purpose: 'Medical',
          amount: '500.00',
        },
        {
          id: uuid(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-01-15',
          purpose: 'Education',
          amount: '1000.00',
        },
        {
          id: uuid(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-02-01',
          purpose: 'Medical',
          amount: '750.00',
        },
      ]

      for (const transaction of testTransactions) {
        await repo.create(transaction)
      }
    })

    it('should calculate total amount for voter', async () => {
      // Act
      const total = await repo.getTotalAmountByVoter(voterId)

      // Assert
      expect(total).toBe(2250.00) // 500 + 1000 + 750
    })

    it('should calculate total amount by purpose', async () => {
      // Act
      const medicalTotal = await repo.getTotalAmountByPurpose('Medical')

      // Assert
      expect(medicalTotal).toBe(1250.00) // 500 + 750
    })

    it('should calculate total amount by date range', async () => {
      // Act
      const januaryTotal = await repo.getTotalAmountByDateRange('2024-01-01', '2024-01-31')

      // Assert
      expect(januaryTotal).toBe(1500.00) // 500 + 1000
    })

    it('should get transaction summary by purpose', async () => {
      // Act
      const summary = await repo.getTransactionSummaryByPurpose()

      // Assert
      expect(summary.length).toBe(2)

      const medicalSummary = summary.find(s => s.purpose === 'Medical')
      const educationSummary = summary.find(s => s.purpose === 'Education')

      expect(medicalSummary?.count).toBe(2)
      expect(medicalSummary?.totalAmount).toBe(1250.00)
      expect(educationSummary?.count).toBe(1)
      expect(educationSummary?.totalAmount).toBe(1000.00)
    })
  })

  describe('Filtering and Pagination', () => {
    beforeEach(async () => {
      // Create test transactions for filtering
      const testTransactions = [
        {
          id: uuid(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-01-01',
          purpose: 'Medical assistance',
          amount: '500.00',
        },
        {
          id: uuid(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          syncStatus: 'pending',
          voterId: voterId,
          date: '2024-01-15',
          purpose: 'Educational support',
          amount: '1000.00',
        },
        {
          id: uuid(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          syncStatus: 'synced',
          voterId: voterId,
          date: '2024-02-01',
          purpose: 'Emergency aid',
          amount: '750.00',
        },
      ]

      for (const transaction of testTransactions) {
        await repo.create(transaction)
      }
    })

    it('should filter transactions by sync status', async () => {
      // Act
      const syncedTransactions = await repo.findMany({ syncStatus: 'synced' })

      // Assert
      expect(syncedTransactions.length).toBe(2)
      syncedTransactions.forEach(transaction => {
        expect(transaction.syncStatus).toBe('synced')
      })
    })

    it('should filter transactions by amount range', async () => {
      // Act
      const highValueTransactions = await repo.findMany({
        minAmount: 750.00,
        maxAmount: 1000.00
      })

      // Assert
      expect(highValueTransactions.length).toBe(2)
      highValueTransactions.forEach(transaction => {
        const amount = parseFloat(transaction.amount)
        expect(amount >= 750.00).toBe(true)
        expect(amount <= 1000.00).toBe(true)
      })
    })

    it('should support pagination', async () => {
      // Act
      const page1 = await repo.findMany({ limit: 2, offset: 0 })
      const page2 = await repo.findMany({ limit: 2, offset: 2 })

      // Assert
      expect(page1.length).toBe(2)
      expect(page2.length).toBe(1)

      // Ensure no duplicates between pages
      const page1Ids = page1.map(t => t.id)
      const page2Ids = page2.map(t => t.id)
      const intersection = page1Ids.filter(id => page2Ids.includes(id))
      expect(intersection.length).toBe(0)
    })

    it('should combine multiple filters', async () => {
      // Act
      const filteredTransactions = await repo.findMany({
        syncStatus: 'synced',
        minAmount: 600.00
      })

      // Assert
      expect(filteredTransactions.length).toBe(1)
      expect(filteredTransactions[0].syncStatus).toBe('synced')
      expect(parseFloat(filteredTransactions[0].amount)).toBeGreaterThanOrEqual(600.00)
    })
  })

  describe('Caching', () => {
    it('should cache frequently accessed transactions', async () => {
      // Arrange
      const transactionId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: transactionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        voterId: voterId,
        date: '2024-01-01',
        purpose: 'Cached Transaction',
        amount: '100.00',
      })

      // Act - Access same transaction multiple times
      const firstCall = await repo.getById(transactionId)
      const secondCall = await repo.getById(transactionId)

      // Assert
      expect(firstCall).toEqual(secondCall)
      expect(firstCall?.purpose).toBe('Cached Transaction')
    })

    it('should invalidate cache on updates', async () => {
      // Arrange
      const transactionId = uuid()
      const now = new Date().toISOString()
      await repo.create({
        id: transactionId,
        createdAt: now,
        updatedAt: now,
        syncStatus: 'synced',
        voterId: voterId,
        date: '2024-01-01',
        purpose: 'Original Purpose',
        amount: '100.00',
      })

      // Get initial cached value
      const initial = await repo.getById(transactionId)
      expect(initial?.purpose).toBe('Original Purpose')

      // Act - Update the transaction
      await repo.update(transactionId, {
        purpose: 'Updated Purpose',
        updatedAt: new Date().toISOString(),
      })

      // Assert - Should get updated value, not cached
      const updated = await repo.getById(transactionId)
      expect(updated?.purpose).toBe('Updated Purpose')
    })
  })
})