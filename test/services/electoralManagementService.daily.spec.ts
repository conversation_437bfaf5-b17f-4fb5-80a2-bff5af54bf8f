import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ElectoralManagementService } from '@/services/electoralManagementService'
import { z } from 'zod'

const mockVoterTurnoutRepository = {
  getTurnoutStatistics: vi.fn(),
} as unknown as { getTurnoutStatistics?: (...args: unknown[]) => unknown }

const mockSectionRepository = {
  getById: vi.fn(),
} as unknown as { getById: (id: string) => Promise<{ id: string } | null> }

const mockPollingStationRepository = {
  getById: vi.fn(),
} as unknown as { getById: (id: string) => Promise<{ id: string; sectionId: string } | null> }

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('ElectoralManagementService daily summary (Task 3.3)', () => {
  let service: ElectoralManagementService

  beforeEach(() => {
    vi.clearAllMocks()
    service = new ElectoralManagementService(
      mockVoterTurnoutRepository,
      mockSectionRepository,
      mockPollingStationRepository,
    )
  })

  it('validates date input', async () => {
    // @ts-expect-error: invalid params
    await expect(service.getDailySummary({})).rejects.toBeInstanceOf(z.ZodError)
    // @ts-expect-error: invalid date
    await expect(service.getDailySummary({ electionYear: 2024, date: 'bad-date' })).rejects.toBeInstanceOf(z.ZodError)
  })

  it('aggregates daily totals across all sections and stations', async () => {
    const date = '2024-04-19'
    const electionYear = 2024

    const addStationTurnout = vi.fn().mockResolvedValue(undefined)
    const getDailyTurnoutSummary = vi.fn().mockResolvedValue({ date, totalCount: 42 })
    // @ts-expect-error: test hook injection
    service._testHooks = { addStationTurnout, getDailyTurnoutSummary }

    // Use recordTurnout path to simulate multiple entries on same day
    const sectionId = uuid()
    const stationId = uuid()
    mockSectionRepository.getById = vi.fn().mockResolvedValue({ id: sectionId })
    mockPollingStationRepository.getById = vi.fn().mockResolvedValue({ id: stationId, sectionId })

    await service.recordTurnout({ electionYear, sectionId, stationId, count: 10 })
    await service.recordTurnout({ electionYear, sectionId, stationId, count: 32 })

    const summary = await service.getDailySummary({ electionYear, date })
    expect(summary).toEqual({ date, totalCount: 42 })
  })
})
