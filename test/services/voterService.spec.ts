import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import type { Voter } from '@/repositories/voterRepository'
import type { VoterRepository } from '@/repositories/voterRepository'
import { z } from 'zod'
import { VoterService } from '@/services/voterService'

// Minimal mocks for related repositories (if needed later)

// Mock VoterRepository
const mockVoterRepository = {
  create: vi.fn(),
  getById: vi.fn(),
  findByEpicNumber: vi.fn(),
  update: vi.fn(),
  softDelete: vi.fn(),
  findMany: vi.fn(),
  searchByText: vi.fn(),
  count: vi.fn(),
  findBySection: vi.fn(),
  findByPollingStation: vi.fn(),
} as unknown as VoterRepository

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

const baseVoter = (overrides: Partial<Voter> = {}): Voter => ({
  id: uuid(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  syncStatus: 'pending',
  pollingStationId: uuid(),
  sectionId: uuid(),
  name: 'John Doe',
  epicNumber: '**********',
  houseNumber: '12A',
  birthYear: 1990,
  gender: 'M',
  relationshipType: 'S/O',
  status: 'active',
  phone: null,
  email: null,
  facebook: null,
  instagram: null,
  twitter: null,
  supporterStatus: null,
  education: null,
  occupation: null,
  community: null,
  religion: null,
  economicStatus: null,
  customNotes: null,
  relationshipName: null,
  ...overrides,
})

describe('VoterService (Task 3.2)', () => {
  let service: VoterService

  beforeEach(() => {
    service = new VoterService(mockVoterRepository)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('createVoter', () => {
    it('creates a voter after validating input and EPIC uniqueness', async () => {
      const input = {
        pollingStationId: uuid(),
        sectionId: uuid(),
        name: 'Alice',
        epicNumber: '**********',
        houseNumber: '10',
        birthYear: 1988,
        gender: 'F',
        relationshipType: 'D/O',
        status: 'active',
      }

      mockVoterRepository.findByEpicNumber = vi.fn().mockResolvedValue(null)
      const created = baseVoter({
        epicNumber: input.epicNumber,
        name: input.name,
        pollingStationId: input.pollingStationId,
        sectionId: input.sectionId,
      })
      mockVoterRepository.create = vi.fn().mockResolvedValue(created)

      const result = await service.createVoter(input)

      expect(mockVoterRepository.findByEpicNumber).toHaveBeenCalledWith(
        '**********'
      )
      expect(mockVoterRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
          syncStatus: 'pending',
          epicNumber: '**********',
        })
      )
      expect(result.epicNumber).toBe('**********')
    })

    it('rejects invalid EPIC format', async () => {
      const input = {
        pollingStationId: uuid(),
        sectionId: uuid(),
        name: 'Alice',
        epicNumber: 'BAD',
        houseNumber: '10',
        birthYear: 1988,
        gender: 'F',
        relationshipType: 'D/O',
        status: 'active',
      }

      await expect(service.createVoter(input)).rejects.toBeInstanceOf(
        z.ZodError
      )
      expect(mockVoterRepository.create).not.toHaveBeenCalled()
    })

    it('rejects duplicate EPIC', async () => {
      const input = {
        pollingStationId: uuid(),
        sectionId: uuid(),
        name: 'Alice',
        epicNumber: '**********',
        houseNumber: '10',
        birthYear: 1988,
        gender: 'F',
        relationshipType: 'D/O',
        status: 'active',
      }

      const existing = baseVoter({ epicNumber: input.epicNumber })
      mockVoterRepository.findByEpicNumber = vi.fn().mockResolvedValue(existing)

      await expect(service.createVoter(input)).rejects.toThrow(
        'EPIC number already exists'
      )
      expect(mockVoterRepository.create).not.toHaveBeenCalled()
    })
  })

  describe('updateVoter', () => {
    it('updates allowed fields and preserves uniqueness on EPIC change', async () => {
      const voter = baseVoter()
      mockVoterRepository.getById = vi.fn().mockResolvedValue(voter)
      mockVoterRepository.update = vi.fn().mockResolvedValue(true)

      const patch = { name: 'Alice Updated', epicNumber: '**********' }
      mockVoterRepository.findByEpicNumber = vi.fn().mockResolvedValue(null)

      const updated = await service.updateVoter(voter.id, patch)

      expect(mockVoterRepository.update).toHaveBeenCalledWith(
        voter.id,
        expect.objectContaining({
          updatedAt: expect.any(String),
          name: 'Alice Updated',
          epicNumber: '**********',
        })
      )
      expect(updated).toBe(true)
    })

    it('rejects EPIC change if duplicate exists', async () => {
      const voter = baseVoter({ epicNumber: '**********' })
      mockVoterRepository.getById = vi.fn().mockResolvedValue(voter)

      const other = baseVoter({ epicNumber: '**********' })
      mockVoterRepository.findByEpicNumber = vi.fn().mockResolvedValue(other)

      await expect(
        service.updateVoter(voter.id, { epicNumber: '**********' })
      ).rejects.toThrow('EPIC number already exists')
      expect(mockVoterRepository.update).not.toHaveBeenCalled()
    })
  })

  describe('deleteVoter', () => {
    it('soft-deletes a voter', async () => {
      const voter = baseVoter()
      mockVoterRepository.softDelete = vi.fn().mockResolvedValue(true)

      const result = await service.deleteVoter(voter.id)
      expect(mockVoterRepository.softDelete).toHaveBeenCalledWith(
        voter.id,
        expect.any(String)
      )
      expect(result).toBe(true)
    })
  })

  describe('listing and search', () => {
    it('lists voters with filters and counts', async () => {
      const voters = [baseVoter(), baseVoter()]
      mockVoterRepository.findMany = vi.fn().mockResolvedValue(voters)
      mockVoterRepository.count = vi.fn().mockResolvedValue(2)

      const result = await service.listVoters({ limit: 10, offset: 0 })
      expect(mockVoterRepository.findMany).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
      })
      expect(mockVoterRepository.count).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
      })
      expect(result.items.length).toBe(2)
      expect(result.total).toBe(2)
    })

    it('searches by text using repository FTS', async () => {
      const voters = [baseVoter({ name: 'Alice' })]
      mockVoterRepository.searchByText = vi.fn().mockResolvedValue(voters)

      const result = await service.search('Alice')
      expect(mockVoterRepository.searchByText).toHaveBeenCalledWith('Alice', 50)
      expect(result).toEqual(voters)
    })
  })

  describe('bulkImport', () => {
    it('imports voters with progress and partial failures', async () => {
      const inputs = [
        {
          pollingStationId: uuid(),
          sectionId: uuid(),
          name: 'Alice',
          epicNumber: '**********',
          houseNumber: '10',
          birthYear: 1988,
          gender: 'F',
          relationshipType: 'D/O',
          status: 'active',
        },
        {
          pollingStationId: uuid(),
          sectionId: uuid(),
          name: 'Bob',
          epicNumber: '**********', // duplicate EPIC
          houseNumber: '11',
          birthYear: 1985,
          gender: 'M',
          relationshipType: 'S/O',
          status: 'active',
        },
      ]

      mockVoterRepository.findByEpicNumber = vi
        .fn()
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(baseVoter({ epicNumber: '**********' }))
      mockVoterRepository.create = vi
        .fn()
        .mockResolvedValue(baseVoter({ epicNumber: '**********' }))

      const progress = vi.fn()
      const summary = await service.bulkImport(inputs, { progress })

      expect(progress).toHaveBeenCalledTimes(2)
      expect(summary.processed).toBe(2)
      expect(summary.succeeded).toBe(1)
      expect(summary.failed).toBe(1)
      expect(summary.errors[0].message).toContain('EPIC number already exists')
    })
  })
})
