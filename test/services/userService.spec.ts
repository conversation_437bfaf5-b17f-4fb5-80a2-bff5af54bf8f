import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { UserService } from '@/services/userService'
import type { UserRepository } from '@/repositories/userRepository'
import type { User } from '@/repositories/userRepository'

// Mock the UserRepository
const mockUserRepository = {
  create: vi.fn(),
  getById: vi.fn(),
  getByUsername: vi.fn(),
  update: vi.fn(),
  softDelete: vi.fn(),
  authenticate: vi.fn(),
  findMany: vi.fn(),
} as unknown as UserRepository

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('UserService (Task 3.1)', () => {
  let userService: UserService

  beforeEach(() => {
    userService = new UserService(mockUserRepository)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('User Registration', () => {
    it('should create a new user with hashed password', async () => {
      // Arrange
      const userData = {
        username: 'testuser',
        password: 'SecurePass123!',
        role: 'operator' as const,
      }

      const expectedUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'pending',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.getByUsername = vi.fn().mockResolvedValue(null)
      mockUserRepository.create = vi.fn().mockResolvedValue(expectedUser)

      // Act
      const result = await userService.registerUser(userData)

      // Assert
      expect(mockUserRepository.getByUsername).toHaveBeenCalledWith('testuser')
      expect(mockUserRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          username: 'testuser',
          passwordHash: expect.any(String),
          role: 'operator',
          isActive: true,
        })
      )
      expect(result.username).toBe('testuser')
      expect(result.role).toBe('operator')
    })

    it('should throw error if username already exists', async () => {
      // Arrange
      const userData = {
        username: 'existinguser',
        password: 'SecurePass123!',
        role: 'operator' as const,
      }

      const existingUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'existinguser',
        passwordHash: 'existing_hash',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.getByUsername = vi.fn().mockResolvedValue(existingUser)

      // Act & Assert
      await expect(userService.registerUser(userData)).rejects.toThrow('Username already exists')
      expect(mockUserRepository.create).not.toHaveBeenCalled()
    })

    it('should validate password strength', async () => {
      // Arrange
      const weakPasswordData = {
        username: 'testuser',
        password: '123', // Weak password
        role: 'operator' as const,
      }

      mockUserRepository.getByUsername = vi.fn().mockResolvedValue(null)

      // Act & Assert
      await expect(userService.registerUser(weakPasswordData)).rejects.toThrow()
      expect(mockUserRepository.create).not.toHaveBeenCalled()
    })

    it('should validate role permissions', async () => {
      // Arrange
      const invalidRoleData = {
        username: 'testuser',
        password: 'SecurePass123!',
        role: 'invalid_role' as 'admin',
      }

      mockUserRepository.getByUsername = vi.fn().mockResolvedValue(null)

      // Act & Assert
      await expect(userService.registerUser(invalidRoleData)).rejects.toThrow()
      expect(mockUserRepository.create).not.toHaveBeenCalled()
    })
  })

  describe('User Authentication', () => {
    it('should authenticate user with correct credentials', async () => {
      // Arrange
      const credentials = {
        username: 'testuser',
        password: 'SecurePass123!',
      }

      const user: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.authenticate = vi.fn().mockResolvedValue(user)

      // Act
      const result = await userService.authenticateUser(credentials)

      // Assert
      expect(mockUserRepository.authenticate).toHaveBeenCalledWith('testuser', 'SecurePass123!')
      expect(result.success).toBe(true)
      expect(result.user).toEqual(user)
      expect(result.sessionToken).toBeDefined()
    })

    it('should fail authentication with incorrect credentials', async () => {
      // Arrange
      const credentials = {
        username: 'testuser',
        password: 'WrongPassword',
      }

      mockUserRepository.authenticate = vi.fn().mockResolvedValue(null)

      // Act
      const result = await userService.authenticateUser(credentials)

      // Assert
      expect(result.success).toBe(false)
      expect(result.user).toBeNull()
      expect(result.sessionToken).toBeNull()
      expect(result.error).toBe('Invalid username or password')
    })

    it('should fail authentication for inactive user', async () => {
      // Arrange
      const credentials = {
        username: 'inactiveuser',
        password: 'SecurePass123!',
      }

      const inactiveUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'inactiveuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: false,
      }

      mockUserRepository.authenticate = vi.fn().mockResolvedValue(inactiveUser)

      // Act
      const result = await userService.authenticateUser(credentials)

      // Assert
      expect(result.success).toBe(false)
      expect(result.user).toBeNull()
      expect(result.error).toBe('User account is inactive')
    })

    it('should generate unique session tokens', async () => {
      // Arrange
      const credentials = {
        username: 'testuser',
        password: 'SecurePass123!',
      }

      const user: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.authenticate = vi.fn().mockResolvedValue(user)

      // Act
      const result1 = await userService.authenticateUser(credentials)
      const result2 = await userService.authenticateUser(credentials)

      // Assert
      expect(result1.sessionToken).not.toBe(result2.sessionToken)
    })
  })

  describe('Session Management', () => {
    it('should validate active session token', async () => {
      // Arrange
      const user: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.authenticate = vi.fn().mockResolvedValue(user)
      mockUserRepository.getById = vi.fn().mockResolvedValue(user)

      // First authenticate to get a session token
      const authResult = await userService.authenticateUser({
        username: 'testuser',
        password: 'SecurePass123!',
      })

      // Act
      const sessionResult = await userService.validateSession(authResult.sessionToken!)

      // Assert
      expect(sessionResult.valid).toBe(true)
      expect(sessionResult.user).toEqual(user)
    })

    it('should reject invalid session token', async () => {
      // Act
      const result = await userService.validateSession('invalid_token')

      // Assert
      expect(result.valid).toBe(false)
      expect(result.user).toBeNull()
      expect(result.error).toBe('Invalid or expired session')
    })

    it('should reject expired session token', async () => {
      // Arrange
      const user: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.authenticate = vi.fn().mockResolvedValue(user)

      // Mock Date.now to simulate time passing
      const originalNow = Date.now
      Date.now = vi.fn().mockReturnValue(1000000) // Initial time

      const authResult = await userService.authenticateUser({
        username: 'testuser',
        password: 'SecurePass123!',
      })

      // Simulate time passing beyond session expiry
      Date.now = vi.fn().mockReturnValue(1000000 + 25 * 60 * 60 * 1000) // 25 hours later

      // Act
      const result = await userService.validateSession(authResult.sessionToken!)

      // Assert
      expect(result.valid).toBe(false)
      expect(result.error).toBe('Invalid or expired session')

      // Restore Date.now
      Date.now = originalNow
    })

    it('should invalidate session on logout', async () => {
      // Arrange
      const user: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.authenticate = vi.fn().mockResolvedValue(user)

      const authResult = await userService.authenticateUser({
        username: 'testuser',
        password: 'SecurePass123!',
      })

      // Act
      await userService.logout(authResult.sessionToken!)
      const sessionResult = await userService.validateSession(authResult.sessionToken!)

      // Assert
      expect(sessionResult.valid).toBe(false)
      expect(sessionResult.error).toBe('Invalid or expired session')
    })
  })

  describe('User Management', () => {
    it('should get user by ID', async () => {
      // Arrange
      const userId = uuid()
      const user: User = {
        id: userId,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.getById = vi.fn().mockResolvedValue(user)

      // Act
      const result = await userService.getUserById(userId)

      // Assert
      expect(mockUserRepository.getById).toHaveBeenCalledWith(userId)
      expect(result).toEqual(user)
    })

    it('should update user profile', async () => {
      // Arrange
      const userId = uuid()
      const updateData = {
        role: 'admin' as const,
        isActive: false,
      }

      const updatedUser: User = {
        id: userId,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
        syncStatus: 'pending',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'admin',
        isActive: false,
      }

      mockUserRepository.update = vi.fn().mockResolvedValue(true)
      mockUserRepository.getById = vi.fn().mockResolvedValue(updatedUser)

      // Act
      const result = await userService.updateUser(userId, updateData)

      // Assert
      expect(mockUserRepository.update).toHaveBeenCalledWith(
        userId,
        expect.objectContaining({
          role: 'admin',
          isActive: false,
          updatedAt: expect.any(String),
        })
      )
      expect(result).toEqual(updatedUser)
    })

    it('should change user password', async () => {
      // Arrange
      const userId = uuid()
      const passwordData = {
        currentPassword: 'OldPass123!',
        newPassword: 'NewPass456!',
      }

      const user: User = {
        id: userId,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'old_hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.getById = vi.fn().mockResolvedValue(user)
      mockUserRepository.authenticate = vi.fn().mockResolvedValue(user)
      mockUserRepository.update = vi.fn().mockResolvedValue(true)

      // Act
      const result = await userService.changePassword(userId, passwordData)

      // Assert
      expect(mockUserRepository.authenticate).toHaveBeenCalledWith('testuser', 'OldPass123!')
      expect(mockUserRepository.update).toHaveBeenCalledWith(
        userId,
        expect.objectContaining({
          passwordHash: expect.any(String),
          updatedAt: expect.any(String),
        })
      )
      expect(result).toBe(true)
    })

    it('should fail password change with incorrect current password', async () => {
      // Arrange
      const userId = uuid()
      const passwordData = {
        currentPassword: 'WrongPass123!',
        newPassword: 'NewPass456!',
      }

      const user: User = {
        id: userId,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'testuser',
        passwordHash: 'hashed_password',
        role: 'operator',
        isActive: true,
      }

      mockUserRepository.getById = vi.fn().mockResolvedValue(user)
      mockUserRepository.authenticate = vi.fn().mockResolvedValue(null)

      // Act & Assert
      await expect(userService.changePassword(userId, passwordData)).rejects.toThrow('Current password is incorrect')
      expect(mockUserRepository.update).not.toHaveBeenCalled()
    })

    it('should deactivate user account', async () => {
      // Arrange
      const userId = uuid()
      mockUserRepository.update = vi.fn().mockResolvedValue(true)

      // Act
      const result = await userService.deactivateUser(userId)

      // Assert
      expect(mockUserRepository.update).toHaveBeenCalledWith(
        userId,
        expect.objectContaining({
          isActive: false,
          updatedAt: expect.any(String),
        })
      )
      expect(result).toBe(true)
    })

    it('should list users with filtering', async () => {
      // Arrange
      const filters = {
        role: 'operator' as const,
        isActive: true,
      }

      const users: User[] = [
        {
          id: uuid(),
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          syncStatus: 'synced',
          username: 'user1',
          passwordHash: 'hash1',
          role: 'operator',
          isActive: true,
        },
        {
          id: uuid(),
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          syncStatus: 'synced',
          username: 'user2',
          passwordHash: 'hash2',
          role: 'operator',
          isActive: true,
        },
      ]

      mockUserRepository.findMany = vi.fn().mockResolvedValue(users)

      // Act
      const result = await userService.listUsers(filters)

      // Assert
      expect(mockUserRepository.findMany).toHaveBeenCalledWith(filters)
      expect(result).toEqual(users)
    })
  })

  describe('Role-Based Access Control', () => {
    it('should check if user has required permission', async () => {
      // Arrange
      const adminUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'admin',
        passwordHash: 'hash',
        role: 'admin',
        isActive: true,
      }

      const operatorUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'operator',
        passwordHash: 'hash',
        role: 'operator',
        isActive: true,
      }

      // Act & Assert
      expect(userService.hasPermission(adminUser, 'manage_users')).toBe(true)
      expect(userService.hasPermission(adminUser, 'view_voters')).toBe(true)
      expect(userService.hasPermission(operatorUser, 'view_voters')).toBe(true)
      expect(userService.hasPermission(operatorUser, 'manage_users')).toBe(false)
    })

    it('should validate role hierarchy', async () => {
      // Arrange
      const adminUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'admin',
        passwordHash: 'hash',
        role: 'admin',
        isActive: true,
      }

      const operatorUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'operator',
        passwordHash: 'hash',
        role: 'operator',
        isActive: true,
      }

      const viewerUser: User = {
        id: uuid(),
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        syncStatus: 'synced',
        username: 'viewer',
        passwordHash: 'hash',
        role: 'viewer',
        isActive: true,
      }

      // Act & Assert
      expect(userService.canManageUser(adminUser, operatorUser)).toBe(true)
      expect(userService.canManageUser(adminUser, viewerUser)).toBe(true)
      expect(userService.canManageUser(operatorUser, viewerUser)).toBe(true)
      expect(userService.canManageUser(operatorUser, adminUser)).toBe(false)
      expect(userService.canManageUser(viewerUser, operatorUser)).toBe(false)
    })
  })

  describe('Password Security', () => {
    it('should validate password strength requirements', () => {
      // Act & Assert
      expect(() => userService.validatePasswordStrength('123')).toThrow('Password must be at least 8 characters long')
      expect(() => userService.validatePasswordStrength('password')).toThrow('Password must contain at least one uppercase letter')
      expect(() => userService.validatePasswordStrength('PASSWORD')).toThrow('Password must contain at least one lowercase letter')
      expect(() => userService.validatePasswordStrength('Password')).toThrow('Password must contain at least one number')
      expect(() => userService.validatePasswordStrength('Password123')).toThrow('Password must contain at least one special character')
      expect(() => userService.validatePasswordStrength('Password123!')).not.toThrow()
    })

    it('should hash passwords securely', async () => {
      // Arrange
      const password = 'SecurePass123!'

      // Act
      const hash1 = await userService.hashPassword(password)
      const hash2 = await userService.hashPassword(password)

      // Assert
      expect(hash1).not.toBe(password)
      expect(hash2).not.toBe(password)
      expect(hash1).not.toBe(hash2) // Different salts should produce different hashes
      expect(hash1.length).toBeGreaterThan(50) // Argon2 hashes are typically longer
    })

    it('should verify password against hash', async () => {
      // Arrange
      const password = 'SecurePass123!'
      const hash = await userService.hashPassword(password)

      // Act & Assert
      expect(await userService.verifyPassword(password, hash)).toBe(true)
      expect(await userService.verifyPassword('WrongPassword', hash)).toBe(false)
    })
  })
})