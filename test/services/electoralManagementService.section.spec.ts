import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ElectoralManagementService } from '@/services/electoralManagementService'

const mockVoterTurnoutRepository = {
  getTurnoutStatistics: vi.fn(),
} as unknown as { getTurnoutStatistics?: (...args: unknown[]) => unknown }

const mockSectionRepository = {
  getById: vi.fn(),
} as unknown as { getById: (id: string) => Promise<{ id: string } | null> }

const mockPollingStationRepository = {
  getById: vi.fn(),
} as unknown as { getById: (id: string) => Promise<{ id: string; sectionId: string } | null> }

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('ElectoralManagementService per-section aggregation (Task 3.3)', () => {
  let service: ElectoralManagementService

  beforeEach(() => {
    vi.clearAllMocks()
    service = new ElectoralManagementService(
      mockVoterTurnoutRepository,
      mockSectionRepository,
      mockPollingStationRepository,
    )
  })

  it('aggregates multiple stations into section total', async () => {
    const sectionId = uuid()
    const stationA = uuid()
    const stationB = uuid()

    mockSectionRepository.getById = vi.fn().mockResolvedValue({ id: sectionId })
    mockPollingStationRepository.getById = vi.fn().mockImplementation(async (id: string) => {
      if (id === stationA || id === stationB) return { id, sectionId }
      return null
    })

    const now = new Date().toISOString()
    const addStationTurnout = vi.fn().mockResolvedValue(undefined)
    const getSectionTurnoutSummary = vi.fn().mockResolvedValue({ sectionId, totalCount: 12, lastUpdatedAt: now })
    // @ts-expect-error: test hook injection
    service._testHooks = { addStationTurnout, getSectionTurnoutSummary }

    await service.recordTurnout({ electionYear: 2024, sectionId, stationId: stationA, count: 5 })
    await service.recordTurnout({ electionYear: 2024, sectionId, stationId: stationB, count: 7 })

    const sectionSummary = await (
      // @ts-expect-error: test hook call pattern via service
      service._testHooks.getSectionTurnoutSummary({ electionYear: 2024, sectionId })
    )

    expect(addStationTurnout).toHaveBeenCalledTimes(2)
    expect(sectionSummary).toEqual({ sectionId, totalCount: 12, lastUpdatedAt: expect.any(String) })
  })

  it('rejects if sectionId invalid during aggregation query', async () => {
    const badSectionId = uuid()
    mockSectionRepository.getById = vi.fn().mockResolvedValue(null)
    await expect(
      service.recordTurnout({ electionYear: 2024, sectionId: badSectionId, stationId: uuid(), count: 1 })
    ).rejects.toThrow('Invalid section or station')
  })
})
