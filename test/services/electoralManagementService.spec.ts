import { beforeEach, describe, expect, it, vi } from 'vitest'
import { z } from 'zod'
import { ElectoralManagementService } from '@/services/electoralManagementService'

// Mocks
const mockVoterTurnoutRepository = {
  getTurnoutStatistics: vi.fn(),
} as unknown as { getTurnoutStatistics?: (...args: unknown[]) => unknown }

const mockSectionRepository = {
  getById: vi.fn(),
} as unknown as { getById: (id: string) => Promise<{ id: string } | null> }

const mockPollingStationRepository = {
  getById: vi.fn(),
} as unknown as { getById: (id: string) => Promise<{ id: string; sectionId: string } | null> }

function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

describe('ElectoralManagementService (Task 3.3)', () => {
  let service: ElectoralManagementService

  beforeEach(() => {
    vi.clearAllMocks()
    service = new ElectoralManagementService(
      mockVoterTurnoutRepository,
      mockSectionRepository,
      mockPollingStationRepository,
    )
  })

  describe('recordTurnout (per-station)', () => {
    it('validates required input and count > 0 integer', async () => {
      // @ts-expect-error: Testing validation error with invalid input
      await expect(service.recordTurnout({})).rejects.toBeInstanceOf(z.ZodError)

      // invalid count
      // @ts-expect-error: Testing validation error with invalid input
      await expect(service.recordTurnout({ electionYear: 2024, sectionId: uuid(), stationId: uuid(), count: 0 })).rejects.toBeInstanceOf(z.ZodError)

      // invalid electionYear
      // @ts-expect-error: Testing validation error with invalid input
      await expect(service.recordTurnout({ electionYear: 1800, sectionId: uuid(), stationId: uuid(), count: 1 })).rejects.toBeInstanceOf(z.ZodError)
    })

    it('rejects when section or station does not exist', async () => {
      mockSectionRepository.getById = vi.fn().mockResolvedValue(null)
      mockPollingStationRepository.getById = vi.fn().mockResolvedValue(null)

      await expect(
        service.recordTurnout({ electionYear: 2024, sectionId: uuid(), stationId: uuid(), count: 5 })
      ).rejects.toThrow('Invalid section or station')
    })

    it('records turnout and returns aggregated station summary', async () => {
      const sectionId = uuid()
      const stationId = uuid()
      ;mockSectionRepository.getById = vi.fn().mockResolvedValue({ id: sectionId })
      ;mockPollingStationRepository.getById = vi.fn().mockResolvedValue({ id: stationId, sectionId })

      const now = new Date().toISOString()

      const addStationTurnout = vi.fn().mockResolvedValue(undefined)
      const getStationTurnoutSummary = vi.fn().mockResolvedValue({ stationId, totalCount: 7, lastUpdatedAt: now })
      // @ts-expect-error: test hook injection
      service._testHooks = { addStationTurnout, getStationTurnoutSummary }

      const result = await service.recordTurnout({ electionYear: 2024, sectionId, stationId, count: 7 })

      expect(addStationTurnout).toHaveBeenCalledWith({ electionYear: 2024, sectionId, stationId, count: 7 })
      expect(result).toEqual({ stationId, totalCount: 7, lastUpdatedAt: expect.any(String) })
      expect(new Date(result.lastUpdatedAt).toString()).not.toBe('Invalid Date')
    })

    it('enforces unique voter per election when voterId provided', async () => {
      const sectionId = uuid()
      const stationId = uuid()
      const voterId = uuid()
      ;mockSectionRepository.getById = vi.fn().mockResolvedValue({ id: sectionId })
      ;mockPollingStationRepository.getById = vi.fn().mockResolvedValue({ id: stationId, sectionId })

      const markVoterVoted = vi
        .fn()
        .mockResolvedValueOnce(true)
        .mockRejectedValueOnce(new Error('Turnout record already exists'))
      // @ts-expect-error: test hook injection
      service._testHooks = { markVoterVoted }

      const ok = await service.recordTurnout({ electionYear: 2024, sectionId, stationId, count: 1, voterId })
      expect(ok.stationId).toBe(stationId)

      await expect(
        service.recordTurnout({ electionYear: 2024, sectionId, stationId, count: 1, voterId })
      ).rejects.toThrow('Turnout record already exists')
    })
  })
})
