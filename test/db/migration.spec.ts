import { afterEach, beforeEach, describe, expect, it } from 'vitest'
import { TestDb } from '../utils/testDbFactory'
import { MigrationRunner } from '@/db/migrationRunner'
import Database from 'better-sqlite3'
import { drizzle } from 'drizzle-orm/better-sqlite3'

describe('MigrationRunner (Task 2.2)', () => {
  let testDb: TestDb
  let migrationRunner: MigrationRunner

  beforeEach(() => {
    // Create a fresh in-memory database for each test
    const sqlite = new Database(':memory:')
    testDb = {
      db: drizzle(sqlite),
      sqlite,
      dispose: () => sqlite.close(),
    }
    migrationRunner = new MigrationRunner(testDb.sqlite)
  })

  afterEach(() => {
    testDb.dispose()
  })

  describe('runMigrations', () => {
    it('should apply all migrations in correct order', async () => {
      // Act
      await migrationRunner.runMigrations()

      // Assert - Check that all tables exist
      const tables = testDb.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='table'")
        .all() as { name: string }[]

      const tableNames = tables.map((t) => t.name)
      expect(tableNames).toContain('users')
      expect(tableNames).toContain('voters')
      expect(tableNames).toContain('stations')
      expect(tableNames).toContain('sections')
      expect(tableNames).toContain('transactions')
      expect(tableNames).toContain('voter_turnout')
      expect(tableNames).toContain('config_options')
    })

    it('should create FTS5 virtual table for voter search', async () => {
      // Act
      await migrationRunner.runMigrations()

      // Assert - Check FTS5 table exists
      const ftsTable = testDb.sqlite
        .prepare(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='voters_search'"
        )
        .get() as { name: string } | undefined

      expect(ftsTable?.name).toBe('voters_search')

      // Verify it's a virtual table
      const tableInfo = testDb.sqlite
        .prepare("SELECT sql FROM sqlite_master WHERE name='voters_search'")
        .get() as { sql: string } | undefined

      expect(tableInfo?.sql).toContain('VIRTUAL TABLE')
      expect(tableInfo?.sql).toContain('fts5')
    })

    it('should create FTS5 triggers for automatic index updates', async () => {
      // Act
      await migrationRunner.runMigrations()

      // Assert - Check triggers exist
      const triggers = testDb.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='trigger'")
        .all() as { name: string }[]

      const triggerNames = triggers.map((t) => t.name)
      expect(triggerNames).toContain('voters_ai') // after insert
      expect(triggerNames).toContain('voters_ad') // after delete
      expect(triggerNames).toContain('voters_au') // after update
    })

    it('should handle migration idempotency', async () => {
      // Act - Run migrations twice
      await migrationRunner.runMigrations()
      await migrationRunner.runMigrations()

      // Assert - Should not throw and tables should still exist
      const tables = testDb.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='table'")
        .all() as { name: string }[]

      expect(tables.length).toBeGreaterThan(0)
    })

    it('should track applied migrations', async () => {
      // Act
      await migrationRunner.runMigrations()

      // Assert - Check migration tracking table exists
      const migrationTable = testDb.sqlite
        .prepare(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='__drizzle_migrations'"
        )
        .get() as { name: string } | undefined

      expect(migrationTable?.name).toBe('__drizzle_migrations')

      // Check migration records exist
      const appliedMigrations = testDb.sqlite
        .prepare('SELECT * FROM __drizzle_migrations')
        .all()

      expect(appliedMigrations.length).toBeGreaterThan(0)
    })
  })

  describe('FTS5 functionality', () => {
    it('should enable full-text search on voter data', async () => {
      // Arrange
      await migrationRunner.runMigrations()

      // Insert test voter data
      testDb.sqlite.exec(`
        INSERT INTO stations (id, created_at, updated_at, sync_status, station_name, station_number)
        VALUES ('station1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'Station 1', 'ST001');

        INSERT INTO sections (id, created_at, updated_at, sync_status, station_id, section_name, section_number)
        VALUES ('section1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'Section 1', 'SEC001');

        INSERT INTO voters (
          id, created_at, updated_at, sync_status, station_id, section_id,
          name, epic_number, house_number, birth_year, gender, relation_type, status
        ) VALUES (
          'voter1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'section1',
          'John Doe', '**********', '123A', 1990, 'Male', 'Self', 'Active'
        );
      `)

      // Act - Search using FTS5
      const searchResults = testDb.sqlite
        .prepare('SELECT * FROM voters_search WHERE voters_search MATCH ?')
        .all('John') as Array<{
        name: string
        epic_number: string
        house_number: string
        custom_notes: string | null
      }>

      // Assert
      expect(searchResults.length).toBe(1)
      expect(searchResults[0].name).toBe('John Doe')
    })

    it('should automatically update FTS5 index on voter changes', async () => {
      // Arrange
      await migrationRunner.runMigrations()

      // Insert initial data
      testDb.sqlite.exec(`
        INSERT INTO stations (id, created_at, updated_at, sync_status, station_name, station_number)
        VALUES ('station1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'Station 1', 'ST001');

        INSERT INTO sections (id, created_at, updated_at, sync_status, station_id, section_name, section_number)
        VALUES ('section1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'Section 1', 'SEC001');

        INSERT INTO voters (
          id, created_at, updated_at, sync_status, station_id, section_id,
          name, epic_number, house_number, birth_year, gender, relation_type, status
        ) VALUES (
          'voter1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'section1',
          'John Doe', '**********', '123A', 1990, 'Male', 'Self', 'Active'
        );
      `)

      // Act - Update voter name
      testDb.sqlite.exec(`
        UPDATE voters SET name = 'Jane Smith', updated_at = '2024-01-02T00:00:00Z' WHERE id = 'voter1';
      `)

      // Assert - FTS5 index should be updated automatically
      const oldNameResults = testDb.sqlite
        .prepare('SELECT * FROM voters_search WHERE voters_search MATCH ?')
        .all('John') as Array<{
        name: string
        epic_number: string
        house_number: string
        custom_notes: string | null
      }>

      const newNameResults = testDb.sqlite
        .prepare('SELECT * FROM voters_search WHERE voters_search MATCH ?')
        .all('Jane') as Array<{
        name: string
        epic_number: string
        house_number: string
        custom_notes: string | null
      }>

      expect(oldNameResults.length).toBe(0)
      expect(newNameResults.length).toBe(1)
      expect(newNameResults[0].name).toBe('Jane Smith')
    })
  })
})
