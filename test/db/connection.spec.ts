import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { getDB, getSQLite, closeDB } from '../../src/db'
import fs from 'node:fs'
import path from 'node:path'

describe('Database Connection (Task 1.2)', () => {
  const testDbPath = path.join(process.cwd(), 'test-electixir.db')

  beforeEach(() => {
    // Clean up any existing test database
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath)
    }
  })

  afterEach(async () => {
    // Clean up database connections and files
    await closeDB()
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath)
    }
  })

  describe('Database Initialization', () => {
    it('should initialize database connection successfully', async () => {
      // Act
      const db = await getDB()

      // Assert
      expect(db).toBeDefined()
      expect(typeof db.select).toBe('function')
      expect(typeof db.insert).toBe('function')
      expect(typeof db.update).toBe('function')
      expect(typeof db.delete).toBe('function')
    })

    it('should initialize SQLite connection successfully', async () => {
      // Act
      const sqlite = await getSQLite()

      // Assert
      expect(sqlite).toBeDefined()
      expect(typeof sqlite.prepare).toBe('function')
      expect(typeof sqlite.exec).toBe('function')
      expect(typeof sqlite.close).toBe('function')
    })

    it('should return the same instance on multiple calls (singleton pattern)', async () => {
      // Act
      const db1 = await getDB()
      const db2 = await getDB()
      const sqlite1 = await getSQLite()
      const sqlite2 = await getSQLite()

      // Assert
      expect(db1).toBe(db2)
      expect(sqlite1).toBe(sqlite2)
    })

    it('should create database file in correct location', async () => {
      // Arrange - In test environment, should create test-electixir.db
      const expectedPath = path.join(process.cwd(), 'test-electixir.db')

      // Act
      await getDB()

      // Assert
      expect(fs.existsSync(expectedPath)).toBe(true)

      // Cleanup
      if (fs.existsSync(expectedPath)) {
        fs.unlinkSync(expectedPath)
      }
    })
  })

  describe('Database Operations', () => {
    it('should execute basic SQL operations', async () => {
      // Arrange
      const sqlite = await getSQLite()

      // Act & Assert - Should not throw
      expect(() => {
        const result = sqlite.prepare('SELECT 1 as test').get()
        expect(result).toEqual({ test: 1 })
      }).not.toThrow()
    })

    it('should support transactions', async () => {
      // Arrange
      const sqlite = await getSQLite()

      // Act & Assert - Should not throw
      expect(() => {
        const transaction = sqlite.transaction(() => {
          sqlite.prepare('SELECT 1').run()
        })
        transaction()
      }).not.toThrow()
    })

    it('should handle database errors gracefully', async () => {
      // Arrange
      const sqlite = await getSQLite()

      // Act & Assert
      expect(() => {
        sqlite.prepare('SELECT * FROM non_existent_table').get()
      }).toThrow()
    })
  })

  describe('Connection Management', () => {
    it('should close database connections properly', async () => {
      // Arrange
      const sqlite = await getSQLite()
      expect(sqlite.open).toBe(true)

      // Act
      await closeDB()

      // Assert
      expect(sqlite.open).toBe(false)
    })

    it('should handle multiple close calls gracefully', async () => {
      // Arrange
      await getDB()

      // Act & Assert - Should not throw
      await expect(closeDB()).resolves.not.toThrow()
      await expect(closeDB()).resolves.not.toThrow()
    })

    it('should allow reconnection after close', async () => {
      // Arrange
      await getDB()
      await closeDB()

      // Act
      const newDb = await getDB()

      // Assert
      expect(newDb).toBeDefined()
      expect(typeof newDb.select).toBe('function')
    })
  })

  describe('Environment Configuration', () => {
    it('should use test database path in test environment', async () => {
      // Arrange
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'test'

      try {
        // Act
        await getDB()

        // Assert - Should create test database
        const testDbExists = fs.existsSync(
          path.join(process.cwd(), 'test-electixir.db')
        )
        expect(testDbExists).toBe(true)
      } finally {
        // Cleanup
        process.env.NODE_ENV = originalEnv
      }
    })

    it('should use production database path in production environment', async () => {
      // Arrange
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      try {
        // Act
        await getDB()

        // Assert - Should create production database
        const prodDbExists = fs.existsSync(
          path.join(process.cwd(), 'electixir.db')
        )
        expect(prodDbExists).toBe(true)

        // Cleanup
        if (prodDbExists) {
          fs.unlinkSync(path.join(process.cwd(), 'electixir.db'))
        }
      } finally {
        // Cleanup
        process.env.NODE_ENV = originalEnv
      }
    })
  })

  describe('Migration Integration', () => {
    it('should run migrations automatically on database initialization', async () => {
      // Act
      const db = await getDB()
      const sqlite = await getSQLite()

      // Assert - Check that migrations have been applied
      const tables = sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='table'")
        .all() as { name: string }[]

      const tableNames = tables.map((t) => t.name)
      expect(tableNames).toContain('stations')
      expect(tableNames).toContain('voters')
      expect(tableNames).toContain('voters_search')
    })

    it('should handle migration errors gracefully', async () => {
      // Arrange - Mock a migration failure
      const originalConsoleError = console.error
      console.error = vi.fn()

      try {
        // This test would require mocking the migration system
        // For now, we'll just ensure the database initializes
        const db = await getDB()
        expect(db).toBeDefined()
      } finally {
        console.error = originalConsoleError
      }
    })
  })
})
