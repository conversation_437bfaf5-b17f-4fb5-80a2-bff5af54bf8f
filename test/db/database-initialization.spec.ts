import { afterEach, beforeEach, describe, expect, it } from 'vitest'
import { getDB, getSQLite, closeDB } from '@/db/index'
import fs from 'node:fs'
import path from 'node:path'

describe('Database Initialization (Task 2.2)', () => {
  const testDbPath = path.resolve('.data/test-init.sqlite')

  beforeEach(() => {
    // Ensure clean state before each test
    closeDB()
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath)
    }
    // Clean up any nested test directories
    const nestedDir = path.resolve('.data/nested')
    if (fs.existsSync(nestedDir)) {
      fs.rmSync(nestedDir, { recursive: true })
    }
  })

  afterEach(() => {
    // Clean up test database
    closeDB()
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath)
    }
    // Clean up environment
    delete process.env.DB_PATH
  })

  it('should run migrations on first database access', async () => {
    // Set test database path
    process.env.DB_PATH = testDbPath

    // Act
    await getDB()

    // Assert - Check that migrations have been applied
    const sqlite = await getSQLite()
    const tables = sqlite
      .prepare("SELECT name FROM sqlite_master WHERE type='table'")
      .all() as { name: string }[]

    const tableNames = tables.map(t => t.name)
    expect(tableNames).toContain('users')
    expect(tableNames).toContain('voters')
    expect(tableNames).toContain('voters_search')
    expect(tableNames).toContain('__drizzle_migrations')

    // Verify FTS5 is properly set up
    const ftsTable = sqlite
      .prepare("SELECT sql FROM sqlite_master WHERE name='voters_search'")
      .get() as { sql: string } | undefined

    expect(ftsTable?.sql).toContain('VIRTUAL TABLE')
    expect(ftsTable?.sql).toContain('fts5')

  })

  it('should return same instance on subsequent calls', async () => {
    // Set test database path
    process.env.DB_PATH = testDbPath

    // Act
    const db1 = await getDB()
    const db2 = await getDB()

    // Assert
    expect(db1).toBe(db2) // Same instance
  })


})