import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createTestDb, type TestDb } from '../utils/testDbFactory'

describe('Database Schema (Task 1.2)', () => {
  let testDb: TestDb

  beforeEach(() => {
    testDb = createTestDb()
  })

  afterEach(() => {
    testDb.dispose()
  })

  describe('Table Creation', () => {
    it('should create all required tables with correct structure', () => {
      // Arrange & Act
      const tables = testDb.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        .all() as { name: string }[]

      const tableNames = tables.map((t) => t.name)

      // Assert - Check all required tables exist
      expect(tableNames).toContain('stations')
      expect(tableNames).toContain('sections')
      expect(tableNames).toContain('voters')
      expect(tableNames).toContain('voter_turnout')
      expect(tableNames).toContain('transactions')
      expect(tableNames).toContain('users')
      expect(tableNames).toContain('config_options')
    })

    it('should create stations table with correct columns', () => {
      // Act
      const columns = testDb.sqlite
        .prepare("PRAGMA table_info(stations)")
        .all() as Array<{ name: string; type: string; notnull: number; pk: number }>

      const columnNames = columns.map((c) => c.name)

      // Assert
      expect(columnNames).toContain('id')
      expect(columnNames).toContain('created_at')
      expect(columnNames).toContain('updated_at')
      expect(columnNames).toContain('deleted_at')
      expect(columnNames).toContain('sync_status')
      expect(columnNames).toContain('last_sync_at')
      expect(columnNames).toContain('station_name')
      expect(columnNames).toContain('station_number')

      // Check primary key
      const idColumn = columns.find((c) => c.name === 'id')
      expect(idColumn?.pk).toBe(1)
      expect(idColumn?.notnull).toBe(1)
    })

    it('should create voters table with correct columns and foreign keys', () => {
      // Act
      const columns = testDb.sqlite
        .prepare("PRAGMA table_info(voters)")
        .all() as Array<{ name: string; type: string; notnull: number }>

      const columnNames = columns.map((c) => c.name)

      // Assert - Check all required columns exist
      expect(columnNames).toContain('id')
      expect(columnNames).toContain('station_id')
      expect(columnNames).toContain('section_id')
      expect(columnNames).toContain('name')
      expect(columnNames).toContain('epic_number')
      expect(columnNames).toContain('house_number')
      expect(columnNames).toContain('birth_year')
      expect(columnNames).toContain('gender')
      expect(columnNames).toContain('relation_type')
      expect(columnNames).toContain('relation_name')
      expect(columnNames).toContain('status')
      expect(columnNames).toContain('custom_notes')

      // Check foreign key constraints
      const foreignKeys = testDb.sqlite
        .prepare("PRAGMA foreign_key_list(voters)")
        .all() as Array<{ table: string; from: string; to: string }>

      expect(foreignKeys).toHaveLength(2)
      expect(foreignKeys.some((fk) => fk.table === 'stations' && fk.from === 'station_id')).toBe(true)
      expect(foreignKeys.some((fk) => fk.table === 'sections' && fk.from === 'section_id')).toBe(true)
    })
  })

  describe('Unique Constraints', () => {
    it('should enforce unique station_number constraint', () => {
      // Arrange
      testDb.sqlite.exec(`
        INSERT INTO stations (id, created_at, updated_at, sync_status, station_name, station_number)
        VALUES ('station1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'Station 1', 'ST001')
      `)

      // Act & Assert
      expect(() => {
        testDb.sqlite.exec(`
          INSERT INTO stations (id, created_at, updated_at, sync_status, station_name, station_number)
          VALUES ('station2', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'Station 2', 'ST001')
        `)
      }).toThrow()
    })

    it('should enforce unique epic_number constraint', () => {
      // Arrange
      testDb.sqlite.exec(`
        INSERT INTO stations (id, created_at, updated_at, sync_status, station_name, station_number)
        VALUES ('station1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'Station 1', 'ST001');
        
        INSERT INTO sections (id, created_at, updated_at, sync_status, station_id, section_name, section_number)
        VALUES ('section1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'Section A', 'SEC001');
        
        INSERT INTO voters (id, created_at, updated_at, sync_status, station_id, section_id, name, epic_number, house_number, birth_year, gender, relation_type, status)
        VALUES ('voter1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'section1', 'John Doe', '**********', '123', 1990, 'Male', 'Self', 'Active')
      `)

      // Act & Assert
      expect(() => {
        testDb.sqlite.exec(`
          INSERT INTO voters (id, created_at, updated_at, sync_status, station_id, section_id, name, epic_number, house_number, birth_year, gender, relation_type, status)
          VALUES ('voter2', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'section1', 'Jane Doe', '**********', '124', 1991, 'Female', 'Self', 'Active')
        `)
      }).toThrow()
    })
  })

  describe('FTS5 Virtual Table', () => {
    it('should create voters_search FTS5 virtual table', () => {
      // Act
      const tables = testDb.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='voters_search'")
        .all() as { name: string }[]

      // Assert
      expect(tables).toHaveLength(1)
      expect(tables[0].name).toBe('voters_search')
    })

    it('should have FTS5 triggers for automatic index updates', () => {
      // Act
      const triggers = testDb.sqlite
        .prepare("SELECT name FROM sqlite_master WHERE type='trigger' AND name LIKE 'voters_%'")
        .all() as { name: string }[]

      const triggerNames = triggers.map((t) => t.name)

      // Assert
      expect(triggerNames).toContain('voters_ai') // After Insert
      expect(triggerNames).toContain('voters_ad') // After Delete
      expect(triggerNames).toContain('voters_au') // After Update
    })

    it('should automatically update FTS5 index when voter data changes', () => {
      // Arrange
      testDb.sqlite.exec(`
        INSERT INTO stations (id, created_at, updated_at, sync_status, station_name, station_number)
        VALUES ('station1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'Station 1', 'ST001');
        
        INSERT INTO sections (id, created_at, updated_at, sync_status, station_id, section_name, section_number)
        VALUES ('section1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'Section A', 'SEC001');
        
        INSERT INTO voters (id, created_at, updated_at, sync_status, station_id, section_id, name, epic_number, house_number, birth_year, gender, relation_type, status)
        VALUES ('voter1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', 'synced', 'station1', 'section1', 'John Doe', '**********', '123', 1990, 'Male', 'Self', 'Active')
      `)

      // Act - Search using FTS5
      const searchResults = testDb.sqlite
        .prepare('SELECT * FROM voters_search WHERE voters_search MATCH ?')
        .all('John') as Array<{ name: string; epic_number: string }>

      // Assert
      expect(searchResults).toHaveLength(1)
      expect(searchResults[0].name).toBe('John Doe')
      expect(searchResults[0].epic_number).toBe('**********')
    })
  })
})
