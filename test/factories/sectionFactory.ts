import { v4 as uuidv4 } from 'uuid'
import type { Section } from '@/shared/schemas/sectionSchemas'
import { StationFactory } from './stationFactory'

/**
 * Factory for creating test section data
 * Follows the database schema exactly as defined in docs/database-design.mmd
 */
export class SectionFactory {
  private static counter = 1

  /**
   * Create a section with realistic test data
   */
  static create(overrides: Partial<Section> = {}): Section {
    const counter = this.counter++
    const sectionNumber = `SEC${counter.toString().padStart(3, '0')}`
    
    // Create a default station if stationId is not provided
    const defaultStationId = overrides.stationId || StationFactory.create().id
    
    return {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: new Date().toISOString(),
      stationId: defaultStationId,
      sectionName: `Section ${String.fromCharCode(64 + counter)}`, // A, B, C, etc.
      sectionNumber,
      ...overrides,
    }
  }

  /**
   * Create multiple sections for the same station
   */
  static createManyForStation(stationId: string, count: number, overrides: Partial<Section> = {}): Section[] {
    return Array.from({ length: count }, () => this.create({ stationId, ...overrides }))
  }

  /**
   * Create sections with alphabetical naming (A, B, C, etc.)
   */
  static createAlphabetical(stationId: string, count: number): Section[] {
    return Array.from({ length: count }, (_, index) => {
      const letter = String.fromCharCode(65 + index) // A, B, C, etc.
      return this.create({
        stationId,
        sectionName: `Section ${letter}`,
        sectionNumber: `SEC${letter}${(index + 1).toString().padStart(2, '0')}`,
      })
    })
  }

  /**
   * Create a section with pending sync status
   */
  static createPending(overrides: Partial<Section> = {}): Section {
    return this.create({
      syncStatus: 'pending',
      lastSyncAt: null,
      ...overrides,
    })
  }

  /**
   * Create a section with conflict status
   */
  static createConflicted(overrides: Partial<Section> = {}): Section {
    return this.create({
      syncStatus: 'conflict',
      ...overrides,
    })
  }

  /**
   * Create a soft-deleted section
   */
  static createDeleted(overrides: Partial<Section> = {}): Section {
    return this.create({
      deletedAt: new Date().toISOString(),
      ...overrides,
    })
  }

  /**
   * Reset the counter for consistent test data
   */
  static resetCounter(): void {
    this.counter = 1
  }
}

/**
 * Raw section data for database insertion (without Zod validation)
 */
export interface RawSectionData {
  id: string
  created_at: string
  updated_at: string
  deleted_at: string | null
  sync_status: string
  last_sync_at: string | null
  station_id: string
  section_name: string
  section_number: string
}

/**
 * Factory for creating raw section data for direct database insertion
 */
export class RawSectionFactory {
  /**
   * Convert Section to raw database format
   */
  static toRaw(section: Section): RawSectionData {
    return {
      id: section.id,
      created_at: section.createdAt,
      updated_at: section.updatedAt,
      deleted_at: section.deletedAt,
      sync_status: section.syncStatus,
      last_sync_at: section.lastSyncAt,
      station_id: section.stationId,
      section_name: section.sectionName,
      section_number: section.sectionNumber,
    }
  }

  /**
   * Create raw section data directly
   */
  static createRaw(overrides: Partial<RawSectionData> = {}): RawSectionData {
    const section = SectionFactory.create()
    return {
      ...this.toRaw(section),
      ...overrides,
    }
  }

  /**
   * Create multiple raw section records for the same station
   */
  static createManyRawForStation(stationId: string, count: number, overrides: Partial<RawSectionData> = {}): RawSectionData[] {
    return Array.from({ length: count }, () => this.createRaw({ station_id: stationId, ...overrides }))
  }
}
