import { v4 as uuidv4 } from 'uuid'
import type { Voter } from '@/shared/schemas/voterSchemas'
import { StationFactory } from './stationFactory'
import { SectionFactory } from './sectionFactory'

/**
 * Factory for creating test voter data
 * Follows the database schema exactly as defined in docs/database-design.mmd
 */
export class VoterFactory {
  private static counter = 1
  private static epicCounter = 1000000000 // Start from 10-digit number

  // Realistic Indian names for testing
  private static readonly FIRST_NAMES = [
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>sh<PERSON>'
  ]

  private static readonly LAST_NAMES = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>'
  ]

  private static readonly RELATION_TYPES = ['Father', '<PERSON>', 'Husband', 'Others']
  private static readonly GENDERS = ['Male', 'Female', 'Other']
  private static readonly STATUSES = ['Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified']

  /**
   * Create a voter with realistic test data
   */
  static create(overrides: Partial<Voter> = {}): Voter {
    const counter = this.counter++
    const epicCounter = this.epicCounter++
    
    // Generate realistic names
    const firstName = this.FIRST_NAMES[Math.floor(Math.random() * this.FIRST_NAMES.length)]
    const lastName = this.LAST_NAMES[Math.floor(Math.random() * this.LAST_NAMES.length)]
    const fullName = `${firstName} ${lastName}`
    
    // Generate realistic relation name
    const relationType = this.RELATION_TYPES[Math.floor(Math.random() * this.RELATION_TYPES.length)]
    const relationFirstName = this.FIRST_NAMES[Math.floor(Math.random() * this.FIRST_NAMES.length)]
    const relationName = `${relationFirstName} ${lastName}`
    
    // Create default station and section if not provided
    const defaultStationId = overrides.stationId || StationFactory.create().id
    const defaultSectionId = overrides.sectionId || SectionFactory.create({ stationId: defaultStationId }).id
    
    return {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: new Date().toISOString(),
      stationId: defaultStationId,
      sectionId: defaultSectionId,
      name: fullName,
      epicNumber: `ABC${epicCounter.toString()}`,
      houseNumber: `${counter}A`,
      birthYear: 1950 + Math.floor(Math.random() * 50), // Ages 24-74
      gender: this.GENDERS[Math.floor(Math.random() * this.GENDERS.length)] as 'Male' | 'Female' | 'Other',
      relationType: relationType as 'Father' | 'Mother' | 'Husband' | 'Others',
      relationName,
      status: 'Active' as const,
      phone: null,
      email: null,
      facebook: null,
      instagram: null,
      twitter: null,
      supporterStatus: null,
      education: null,
      occupation: null,
      community: null,
      religion: null,
      economicStatus: null,
      customNotes: null,
      ...overrides,
    }
  }

  /**
   * Create multiple voters for the same section
   */
  static createManyForSection(stationId: string, sectionId: string, count: number, overrides: Partial<Voter> = {}): Voter[] {
    return Array.from({ length: count }, () => this.create({ stationId, sectionId, ...overrides }))
  }

  /**
   * Create a voter with complete social media and contact info
   */
  static createWithFullProfile(overrides: Partial<Voter> = {}): Voter {
    const voter = this.create(overrides)
    const firstName = voter.name.split(' ')[0].toLowerCase()
    
    return {
      ...voter,
      phone: `+91${Math.floor(Math.random() * 9000000000) + 1000000000}`,
      email: `${firstName}${Math.floor(Math.random() * 100)}@gmail.com`,
      facebook: `${firstName}.${voter.name.split(' ')[1]?.toLowerCase() || 'user'}`,
      instagram: `@${firstName}_${Math.floor(Math.random() * 1000)}`,
      twitter: `@${firstName}${Math.floor(Math.random() * 100)}`,
      education: 'Graduate',
      occupation: 'Private Job',
      community: 'General',
      religion: 'Hindu',
      economicStatus: 'Middle Class',
      supporterStatus: 'Supporter',
      customNotes: 'Active community member',
      ...overrides,
    }
  }

  /**
   * Create a voter with pending sync status
   */
  static createPending(overrides: Partial<Voter> = {}): Voter {
    return this.create({
      syncStatus: 'pending',
      lastSyncAt: null,
      ...overrides,
    })
  }

  /**
   * Create a voter with specific status
   */
  static createWithStatus(status: 'Active' | 'Expired' | 'Shifted' | 'Duplicate' | 'Missing' | 'Disqualified', overrides: Partial<Voter> = {}): Voter {
    return this.create({
      status,
      ...overrides,
    })
  }

  /**
   * Create voters with specific EPIC number pattern for testing
   */
  static createWithEpicPattern(pattern: string, count: number): Voter[] {
    return Array.from({ length: count }, (_, index) => {
      return this.create({
        epicNumber: `${pattern}${(index + 1).toString().padStart(7, '0')}`,
      })
    })
  }

  /**
   * Create a family of voters (same house number, related)
   */
  static createFamily(stationId: string, sectionId: string, familySize: number = 4): Voter[] {
    const houseNumber = `${Math.floor(Math.random() * 999) + 1}A`
    const lastName = this.LAST_NAMES[Math.floor(Math.random() * this.LAST_NAMES.length)]
    
    return Array.from({ length: familySize }, (_, index) => {
      const firstName = this.FIRST_NAMES[Math.floor(Math.random() * this.FIRST_NAMES.length)]
      const isHead = index === 0
      
      return this.create({
        stationId,
        sectionId,
        name: `${firstName} ${lastName}`,
        houseNumber,
        relationType: isHead ? 'Self' as any : 'Father',
        relationName: isHead ? firstName : `${this.FIRST_NAMES[0]} ${lastName}`,
        birthYear: isHead ? 1970 : 1950 + Math.floor(Math.random() * 50),
      })
    })
  }

  /**
   * Reset counters for consistent test data
   */
  static resetCounters(): void {
    this.counter = 1
    this.epicCounter = 1000000000
  }
}

/**
 * Raw voter data for database insertion (without Zod validation)
 */
export interface RawVoterData {
  id: string
  created_at: string
  updated_at: string
  deleted_at: string | null
  sync_status: string
  last_sync_at: string | null
  station_id: string
  section_id: string
  name: string
  epic_number: string
  house_number: string
  birth_year: number
  gender: string
  relation_type: string
  relation_name: string
  status: string
  phone: string | null
  email: string | null
  facebook: string | null
  instagram: string | null
  twitter: string | null
  supporter_status: string | null
  education: string | null
  occupation: string | null
  community: string | null
  religion: string | null
  economic_status: string | null
  custom_notes: string | null
}

/**
 * Factory for creating raw voter data for direct database insertion
 */
export class RawVoterFactory {
  /**
   * Convert Voter to raw database format
   */
  static toRaw(voter: Voter): RawVoterData {
    return {
      id: voter.id,
      created_at: voter.createdAt,
      updated_at: voter.updatedAt,
      deleted_at: voter.deletedAt,
      sync_status: voter.syncStatus,
      last_sync_at: voter.lastSyncAt,
      station_id: voter.stationId,
      section_id: voter.sectionId,
      name: voter.name,
      epic_number: voter.epicNumber,
      house_number: voter.houseNumber,
      birth_year: voter.birthYear,
      gender: voter.gender,
      relation_type: voter.relationType,
      relation_name: voter.relationName,
      status: voter.status,
      phone: voter.phone,
      email: voter.email,
      facebook: voter.facebook,
      instagram: voter.instagram,
      twitter: voter.twitter,
      supporter_status: voter.supporterStatus,
      education: voter.education,
      occupation: voter.occupation,
      community: voter.community,
      religion: voter.religion,
      economic_status: voter.economicStatus,
      custom_notes: voter.customNotes,
    }
  }

  /**
   * Create raw voter data directly
   */
  static createRaw(overrides: Partial<RawVoterData> = {}): RawVoterData {
    const voter = VoterFactory.create()
    return {
      ...this.toRaw(voter),
      ...overrides,
    }
  }
}
