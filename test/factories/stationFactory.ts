import { v4 as uuidv4 } from 'uuid'
import type { Station } from '@/shared/schemas/stationSchemas'

/**
 * Factory for creating test station data
 * Follows the database schema exactly as defined in docs/database-design.mmd
 */
export class StationFactory {
  private static counter = 1

  /**
   * Create a station with realistic test data
   */
  static create(overrides: Partial<Station> = {}): Station {
    const counter = this.counter++
    const stationNumber = `ST${counter.toString().padStart(3, '0')}`
    
    return {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: new Date().toISOString(),
      stationName: `Station ${counter}`,
      stationNumber,
      ...overrides,
    }
  }

  /**
   * Create multiple stations
   */
  static createMany(count: number, overrides: Partial<Station> = {}): Station[] {
    return Array.from({ length: count }, () => this.create(overrides))
  }

  /**
   * Create a station with pending sync status
   */
  static createPending(overrides: Partial<Station> = {}): Station {
    return this.create({
      syncStatus: 'pending',
      lastSyncAt: null,
      ...overrides,
    })
  }

  /**
   * Create a station with conflict status
   */
  static createConflicted(overrides: Partial<Station> = {}): Station {
    return this.create({
      syncStatus: 'conflict',
      ...overrides,
    })
  }

  /**
   * Create a soft-deleted station
   */
  static createDeleted(overrides: Partial<Station> = {}): Station {
    return this.create({
      deletedAt: new Date().toISOString(),
      ...overrides,
    })
  }

  /**
   * Create stations with specific naming pattern for testing
   */
  static createWithPattern(pattern: string, count: number): Station[] {
    return Array.from({ length: count }, (_, index) => {
      const counter = index + 1
      return this.create({
        stationName: `${pattern} ${counter}`,
        stationNumber: `${pattern.toUpperCase().slice(0, 2)}${counter.toString().padStart(3, '0')}`,
      })
    })
  }

  /**
   * Reset the counter for consistent test data
   */
  static resetCounter(): void {
    this.counter = 1
  }
}

/**
 * Raw station data for database insertion (without Zod validation)
 */
export interface RawStationData {
  id: string
  created_at: string
  updated_at: string
  deleted_at: string | null
  sync_status: string
  last_sync_at: string | null
  station_name: string
  station_number: string
}

/**
 * Factory for creating raw station data for direct database insertion
 */
export class RawStationFactory {
  /**
   * Convert Station to raw database format
   */
  static toRaw(station: Station): RawStationData {
    return {
      id: station.id,
      created_at: station.createdAt,
      updated_at: station.updatedAt,
      deleted_at: station.deletedAt,
      sync_status: station.syncStatus,
      last_sync_at: station.lastSyncAt,
      station_name: station.stationName,
      station_number: station.stationNumber,
    }
  }

  /**
   * Create raw station data directly
   */
  static createRaw(overrides: Partial<RawStationData> = {}): RawStationData {
    const station = StationFactory.create()
    return {
      ...this.toRaw(station),
      ...overrides,
    }
  }

  /**
   * Create multiple raw station records
   */
  static createManyRaw(count: number, overrides: Partial<RawStationData> = {}): RawStationData[] {
    return Array.from({ length: count }, () => this.createRaw(overrides))
  }
}
