import { v4 as uuidv4 } from 'uuid'
import type { User } from '@/shared/schemas/userSchemas'

/**
 * Factory for creating test user data
 * Follows the database schema exactly as defined in docs/database-design.mmd
 */
export class UserFactory {
  private static counter = 1

  /**
   * Create a user with realistic test data
   */
  static create(overrides: Partial<User> = {}): User {
    const counter = this.counter++

    return {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      deletedAt: null,
      syncStatus: 'synced',
      lastSyncAt: new Date().toISOString(),
      username: `user${counter}`,
      passwordHash: '$2b$10$hashedpassword123', // Mock bcrypt hash
      role: 'viewer',
      isActive: true,
      lastLoginAt: null,
      ...overrides,
    }
  }

  /**
   * Create an admin user
   */
  static createAdmin(overrides: Partial<User> = {}): User {
    return this.create({
      username: 'admin',
      role: 'admin',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      ...overrides,
    })
  }

  /**
   * Create an owner user
   */
  static createOwner(overrides: Partial<User> = {}): User {
    return this.create({
      username: 'owner',
      role: 'owner',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      ...overrides,
    })
  }

  /**
   * Create an editor user
   */
  static createEditor(overrides: Partial<User> = {}): User {
    return this.create({
      username: 'editor',
      role: 'editor',
      isActive: true,
      ...overrides,
    })
  }

  /**
   * Create a viewer user
   */
  static createViewer(overrides: Partial<User> = {}): User {
    return this.create({
      username: 'viewer',
      role: 'viewer',
      isActive: true,
      ...overrides,
    })
  }

  /**
   * Create multiple users with different roles
   */
  static createTeam(): User[] {
    return [
      this.createOwner({ username: 'owner' }),
      this.createAdmin({ username: 'admin' }),
      this.createEditor({ username: 'editor1' }),
      this.createEditor({ username: 'editor2' }),
      this.createViewer({ username: 'viewer1' }),
      this.createViewer({ username: 'viewer2' }),
    ]
  }

  /**
   * Reset the counter for consistent test data
   */
  static resetCounter(): void {
    this.counter = 1
  }
}
