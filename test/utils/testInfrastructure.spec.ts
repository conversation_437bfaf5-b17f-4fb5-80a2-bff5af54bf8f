import { describe, it, expect, beforeEach } from 'vitest'
import { StationFactory, RawStationFactory } from '../factories/stationFactory'
import { SectionFactory, RawSectionFactory } from '../factories/sectionFactory'
import { VoterFactory, RawVoterFactory } from '../factories/voterFactory'
import { UserFactory } from '../factories/userFactory'
import { createMockRepositories, resetAllMockRepositories } from './mockRepositories'
import '../assertions/customAssertions'

describe('Test Infrastructure (Task 1.3)', () => {
  beforeEach(() => {
    // Reset factory counters for consistent test data
    StationFactory.resetCounter()
    SectionFactory.resetCounter()
    VoterFactory.resetCounters()
    UserFactory.resetCounter()
  })

  describe('Station Factory', () => {
    it('should create stations with realistic data', () => {
      // Act
      const station = StationFactory.create()

      // Assert
      expect(station).toHaveProperty('id')
      expect(station).toHaveProperty('stationName')
      expect(station).toHaveProperty('stationNumber')
      expect(station.stationNumber).toMatch(/^ST\d{3}$/)
      expect(station.syncStatus).toBe('synced')
      expect(station.deletedAt).toBeNull()
    })

    it('should create stations with overrides', () => {
      // Act
      const station = StationFactory.create({
        stationName: 'Custom Station',
        syncStatus: 'pending'
      })

      // Assert
      expect(station.stationName).toBe('Custom Station')
      expect(station.syncStatus).toBe('pending')
    })

    it('should create multiple stations with unique numbers', () => {
      // Act
      const stations = StationFactory.createMany(3)

      // Assert
      expect(stations).toHaveLength(3)
      const stationNumbers = stations.map(s => s.stationNumber)
      expect(new Set(stationNumbers).size).toBe(3) // All unique
    })

    it('should convert to raw database format', () => {
      // Arrange
      const station = StationFactory.create()

      // Act
      const rawStation = RawStationFactory.toRaw(station)

      // Assert
      expect(rawStation).toHaveProperty('station_name')
      expect(rawStation).toHaveProperty('station_number')
      expect(rawStation).toHaveProperty('created_at')
      expect(rawStation.station_name).toBe(station.stationName)
    })
  })

  describe('Section Factory', () => {
    it('should create sections with realistic data', () => {
      // Act
      const section = SectionFactory.create()

      // Assert
      expect(section).toHaveProperty('id')
      expect(section).toHaveProperty('stationId')
      expect(section).toHaveProperty('sectionName')
      expect(section).toHaveProperty('sectionNumber')
      expect(section.sectionNumber).toMatch(/^SEC\d{3}$/)
    })

    it('should create alphabetical sections', () => {
      // Arrange
      const stationId = 'station-123'

      // Act
      const sections = SectionFactory.createAlphabetical(stationId, 3)

      // Assert
      expect(sections).toHaveLength(3)
      expect(sections[0].sectionName).toBe('Section A')
      expect(sections[1].sectionName).toBe('Section B')
      expect(sections[2].sectionName).toBe('Section C')
      sections.forEach(section => {
        expect(section.stationId).toBe(stationId)
      })
    })
  })

  describe('Voter Factory', () => {
    it('should create voters with realistic data', () => {
      // Act
      const voter = VoterFactory.create()

      // Assert
      expect(voter).toHaveProperty('id')
      expect(voter).toHaveProperty('name')
      expect(voter).toHaveProperty('epicNumber')
      expect(voter).toHaveProperty('houseNumber')
      expect(voter.name).toMatch(/^[A-Za-z]+ [A-Za-z]+$/) // First Last
      expect(voter.epicNumber).toMatch(/^ABC\d+$/)
      expect(voter.birthYear).toBeGreaterThan(1950)
      expect(voter.birthYear).toBeLessThan(2010)
    })

    it('should create voters with full profile', () => {
      // Act
      const voter = VoterFactory.createWithFullProfile()

      // Assert
      expect(voter.phone).toBeTruthy()
      expect(voter.email).toBeTruthy()
      expect(voter.facebook).toBeTruthy()
      expect(voter.education).toBeTruthy()
      expect(voter.occupation).toBeTruthy()
    })

    it('should create family of voters', () => {
      // Arrange
      const stationId = 'station-123'
      const sectionId = 'section-456'

      // Act
      const family = VoterFactory.createFamily(stationId, sectionId, 4)

      // Assert
      expect(family).toHaveLength(4)
      const houseNumbers = family.map(v => v.houseNumber)
      expect(new Set(houseNumbers).size).toBe(1) // Same house number
      family.forEach(voter => {
        expect(voter.stationId).toBe(stationId)
        expect(voter.sectionId).toBe(sectionId)
      })
    })

    it('should convert to raw database format', () => {
      // Arrange
      const voter = VoterFactory.create()

      // Act
      const rawVoter = RawVoterFactory.toRaw(voter)

      // Assert
      expect(rawVoter).toHaveProperty('epic_number')
      expect(rawVoter).toHaveProperty('house_number')
      expect(rawVoter).toHaveProperty('birth_year')
      expect(rawVoter).toHaveProperty('relation_type')
      expect(rawVoter.epic_number).toBe(voter.epicNumber)
    })
  })

  describe('User Factory', () => {
    it('should create users with different roles', () => {
      // Act
      const admin = UserFactory.createAdmin()
      const editor = UserFactory.createEditor()
      const viewer = UserFactory.createViewer()

      // Assert
      expect(admin.role).toBe('admin')
      expect(editor.role).toBe('editor')
      expect(viewer.role).toBe('viewer')
      expect(admin.isActive).toBe(true)
    })

    it('should create a complete team', () => {
      // Act
      const team = UserFactory.createTeam()

      // Assert
      expect(team).toHaveLength(6)
      const roles = team.map(u => u.role)
      expect(roles).toContain('owner')
      expect(roles).toContain('admin')
      expect(roles).toContain('editor')
      expect(roles).toContain('viewer')
    })
  })

  describe('Custom Assertions', () => {
    it('should validate timestamps correctly', () => {
      // Arrange
      const record = {
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T01:00:00Z'
      }

      // Assert
      expect(record).toHaveValidTimestamps()
    })

    it('should validate EPIC numbers correctly', () => {
      // Arrange
      const voter = VoterFactory.create({ epicNumber: '**********' })

      // Assert
      expect(voter).toHaveValidEpicNumber()
    })

    it('should validate sync status correctly', () => {
      // Arrange
      const record = { syncStatus: 'synced' }

      // Assert
      expect(record).toHaveValidSyncStatus()
    })

    it('should validate voter status correctly', () => {
      // Arrange
      const voter = VoterFactory.create({ status: 'Active' })

      // Assert
      expect(voter).toHaveValidVoterStatus()
    })

    it('should validate soft deletion correctly', () => {
      // Arrange
      const activeRecord = { deletedAt: null }
      const deletedRecord = { deletedAt: '2024-01-01T00:00:00Z' }

      // Assert
      expect(activeRecord).toBeActive()
      expect(deletedRecord).toBeSoftDeleted()
    })
  })

  describe('Mock Repositories', () => {
    let repositories: ReturnType<typeof createMockRepositories>

    beforeEach(() => {
      repositories = createMockRepositories()
    })

    it('should create and retrieve users', async () => {
      // Arrange
      const userData = { username: 'testuser', role: 'admin' as const }

      // Act
      const createdUser = await repositories.userRepository.create(userData)
      const retrievedUser = await repositories.userRepository.findById(createdUser.id)

      // Assert
      expect(retrievedUser).toBeTruthy()
      expect(retrievedUser?.username).toBe('testuser')
      expect(retrievedUser?.role).toBe('admin')
    })

    it('should create and search voters', async () => {
      // Arrange
      const voterData = { name: 'John Doe', epicNumber: '**********' }

      // Act
      const createdVoter = await repositories.voterRepository.create(voterData)
      const searchResults = await repositories.voterRepository.search('John')

      // Assert
      expect(searchResults).toHaveLength(1)
      expect(searchResults[0].name).toBe('John Doe')
    })

    it('should reset mock state correctly', async () => {
      // Arrange
      await repositories.userRepository.create({ username: 'testuser' })
      await repositories.voterRepository.create({ name: 'Test Voter' })

      // Act
      resetAllMockRepositories(repositories)
      const users = await repositories.userRepository.findAll()
      const voters = await repositories.voterRepository.findAll()

      // Assert
      expect(users).toHaveLength(0)
      expect(voters).toHaveLength(0)
    })
  })
})
