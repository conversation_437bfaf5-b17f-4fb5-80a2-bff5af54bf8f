import Database from 'better-sqlite3'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import fs from 'node:fs'
import path from 'node:path'

export type TestDb = {
  db: ReturnType<typeof drizzle>
  sqlite: Database.Database
  dispose: () => void
}

/**
 * Applies Drizzle migrations to the test database
 * @param sqlite - SQLite database instance
 */
function applyMigrations(sqlite: Database.Database): void {
  const migrationsDir = path.resolve('drizzle')

  if (!fs.existsSync(migrationsDir)) {
    throw new Error(`Migrations directory not found: ${migrationsDir}`)
  }

  // Get all .sql files and sort them to ensure proper order
  const migrationFiles = fs
    .readdirSync(migrationsDir)
    .filter((file) => file.endsWith('.sql'))
    .sort()

  for (const file of migrationFiles) {
    const migrationPath = path.join(migrationsDir, file)
    const migrationSql = fs.readFileSync(migrationPath, 'utf-8')

    // Split by statement separator and execute each statement
    const statements = migrationSql
      .split('--> statement-breakpoint')
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0)

    for (const statement of statements) {
      try {
        sqlite.exec(statement)
      } catch (error) {
        // Skip already applied statements (idempotent migrations)
        if (error.message.includes('already exists')) {
          console.warn(
            `Skipping already applied statement in ${file}: ${error.message}`
          )
          continue
        }
        throw new Error(`Failed to execute migration ${file}: ${error}`)
      }
    }
  }
}

export function createTestDb(): TestDb {
  const sqlite = new Database(':memory:')
  const db = drizzle(sqlite)

  // Apply all migrations to set up the schema
  applyMigrations(sqlite)

  const dispose = () => {
    try {
      sqlite.close()
    } catch {
      // noop - database already closed
    }
  }

  return { db, sqlite, dispose }
}
