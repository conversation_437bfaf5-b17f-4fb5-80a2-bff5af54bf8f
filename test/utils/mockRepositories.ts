import { vi } from 'vitest'
import type { UserRepository } from '@/repositories/userRepository'
import type { VoterRepository } from '@/repositories/voterRepository'
import type { StationRepository } from '@/repositories/stationRepository'
import type { SectionRepository } from '@/repositories/sectionRepository'
import { UserFactory } from '../factories/userFactory'
import { VoterFactory } from '../factories/voterFactory'
import { StationFactory } from '../factories/stationFactory'
import { SectionFactory } from '../factories/sectionFactory'

/**
 * Mock UserRepository with realistic behavior
 */
export function createMockUserRepository(): jest.Mocked<UserRepository> {
  const users = new Map()
  
  return {
    findById: vi.fn().mockImplementation(async (id: string) => {
      return users.get(id) || null
    }),
    
    findByUsername: vi.fn().mockImplementation(async (username: string) => {
      for (const user of users.values()) {
        if (user.username === username) {
          return user
        }
      }
      return null
    }),
    
    create: vi.fn().mockImplementation(async (userData) => {
      const user = UserFactory.create(userData)
      users.set(user.id, user)
      return user
    }),
    
    update: vi.fn().mockImplementation(async (id: string, updates) => {
      const user = users.get(id)
      if (!user) return null
      
      const updatedUser = { ...user, ...updates, updatedAt: new Date().toISOString() }
      users.set(id, updatedUser)
      return updatedUser
    }),
    
    delete: vi.fn().mockImplementation(async (id: string) => {
      const user = users.get(id)
      if (!user) return false
      
      const deletedUser = { ...user, deletedAt: new Date().toISOString() }
      users.set(id, deletedUser)
      return true
    }),
    
    findAll: vi.fn().mockImplementation(async () => {
      return Array.from(users.values()).filter(user => !user.deletedAt)
    }),
    
    findByRole: vi.fn().mockImplementation(async (role: string) => {
      return Array.from(users.values()).filter(user => user.role === role && !user.deletedAt)
    }),
    
    updateLastLogin: vi.fn().mockImplementation(async (id: string) => {
      const user = users.get(id)
      if (!user) return null
      
      const updatedUser = { ...user, lastLoginAt: new Date().toISOString() }
      users.set(id, updatedUser)
      return updatedUser
    }),
    
    // Add method to reset mock state for testing
    __reset: () => {
      users.clear()
    },
    
    // Add method to seed mock data
    __seed: (userData: any[]) => {
      userData.forEach(user => users.set(user.id, user))
    }
  } as any
}

/**
 * Mock VoterRepository with realistic behavior
 */
export function createMockVoterRepository(): jest.Mocked<VoterRepository> {
  const voters = new Map()
  
  return {
    findById: vi.fn().mockImplementation(async (id: string) => {
      return voters.get(id) || null
    }),
    
    findByEpicNumber: vi.fn().mockImplementation(async (epicNumber: string) => {
      for (const voter of voters.values()) {
        if (voter.epicNumber === epicNumber) {
          return voter
        }
      }
      return null
    }),
    
    create: vi.fn().mockImplementation(async (voterData) => {
      const voter = VoterFactory.create(voterData)
      voters.set(voter.id, voter)
      return voter
    }),
    
    update: vi.fn().mockImplementation(async (id: string, updates) => {
      const voter = voters.get(id)
      if (!voter) return null
      
      const updatedVoter = { ...voter, ...updates, updatedAt: new Date().toISOString() }
      voters.set(id, updatedVoter)
      return updatedVoter
    }),
    
    delete: vi.fn().mockImplementation(async (id: string) => {
      const voter = voters.get(id)
      if (!voter) return false
      
      const deletedVoter = { ...voter, deletedAt: new Date().toISOString() }
      voters.set(id, deletedVoter)
      return true
    }),
    
    findByStationId: vi.fn().mockImplementation(async (stationId: string) => {
      return Array.from(voters.values()).filter(voter => voter.stationId === stationId && !voter.deletedAt)
    }),
    
    findBySectionId: vi.fn().mockImplementation(async (sectionId: string) => {
      return Array.from(voters.values()).filter(voter => voter.sectionId === sectionId && !voter.deletedAt)
    }),
    
    search: vi.fn().mockImplementation(async (query: string) => {
      return Array.from(voters.values()).filter(voter => 
        !voter.deletedAt && (
          voter.name.toLowerCase().includes(query.toLowerCase()) ||
          voter.epicNumber.toLowerCase().includes(query.toLowerCase()) ||
          voter.houseNumber.toLowerCase().includes(query.toLowerCase())
        )
      )
    }),
    
    findAll: vi.fn().mockImplementation(async () => {
      return Array.from(voters.values()).filter(voter => !voter.deletedAt)
    }),
    
    bulkCreate: vi.fn().mockImplementation(async (votersData) => {
      const createdVoters = votersData.map(data => {
        const voter = VoterFactory.create(data)
        voters.set(voter.id, voter)
        return voter
      })
      return createdVoters
    }),
    
    // Add method to reset mock state for testing
    __reset: () => {
      voters.clear()
    },
    
    // Add method to seed mock data
    __seed: (voterData: any[]) => {
      voterData.forEach(voter => voters.set(voter.id, voter))
    }
  } as any
}

/**
 * Mock StationRepository with realistic behavior
 */
export function createMockStationRepository(): jest.Mocked<StationRepository> {
  const stations = new Map()
  
  return {
    findById: vi.fn().mockImplementation(async (id: string) => {
      return stations.get(id) || null
    }),
    
    findByStationNumber: vi.fn().mockImplementation(async (stationNumber: string) => {
      for (const station of stations.values()) {
        if (station.stationNumber === stationNumber) {
          return station
        }
      }
      return null
    }),
    
    create: vi.fn().mockImplementation(async (stationData) => {
      const station = StationFactory.create(stationData)
      stations.set(station.id, station)
      return station
    }),
    
    update: vi.fn().mockImplementation(async (id: string, updates) => {
      const station = stations.get(id)
      if (!station) return null
      
      const updatedStation = { ...station, ...updates, updatedAt: new Date().toISOString() }
      stations.set(id, updatedStation)
      return updatedStation
    }),
    
    delete: vi.fn().mockImplementation(async (id: string) => {
      const station = stations.get(id)
      if (!station) return false
      
      const deletedStation = { ...station, deletedAt: new Date().toISOString() }
      stations.set(id, deletedStation)
      return true
    }),
    
    findAll: vi.fn().mockImplementation(async () => {
      return Array.from(stations.values()).filter(station => !station.deletedAt)
    }),
    
    // Add method to reset mock state for testing
    __reset: () => {
      stations.clear()
    },
    
    // Add method to seed mock data
    __seed: (stationData: any[]) => {
      stationData.forEach(station => stations.set(station.id, station))
    }
  } as any
}

/**
 * Mock SectionRepository with realistic behavior
 */
export function createMockSectionRepository(): jest.Mocked<SectionRepository> {
  const sections = new Map()
  
  return {
    findById: vi.fn().mockImplementation(async (id: string) => {
      return sections.get(id) || null
    }),
    
    findByStationId: vi.fn().mockImplementation(async (stationId: string) => {
      return Array.from(sections.values()).filter(section => section.stationId === stationId && !section.deletedAt)
    }),
    
    create: vi.fn().mockImplementation(async (sectionData) => {
      const section = SectionFactory.create(sectionData)
      sections.set(section.id, section)
      return section
    }),
    
    update: vi.fn().mockImplementation(async (id: string, updates) => {
      const section = sections.get(id)
      if (!section) return null
      
      const updatedSection = { ...section, ...updates, updatedAt: new Date().toISOString() }
      sections.set(id, updatedSection)
      return updatedSection
    }),
    
    delete: vi.fn().mockImplementation(async (id: string) => {
      const section = sections.get(id)
      if (!section) return false
      
      const deletedSection = { ...section, deletedAt: new Date().toISOString() }
      sections.set(id, deletedSection)
      return true
    }),
    
    findAll: vi.fn().mockImplementation(async () => {
      return Array.from(sections.values()).filter(section => !section.deletedAt)
    }),
    
    // Add method to reset mock state for testing
    __reset: () => {
      sections.clear()
    },
    
    // Add method to seed mock data
    __seed: (sectionData: any[]) => {
      sectionData.forEach(section => sections.set(section.id, section))
    }
  } as any
}

/**
 * Create a complete set of mock repositories for testing
 */
export function createMockRepositories() {
  return {
    userRepository: createMockUserRepository(),
    voterRepository: createMockVoterRepository(),
    stationRepository: createMockStationRepository(),
    sectionRepository: createMockSectionRepository(),
  }
}

/**
 * Reset all mock repositories to clean state
 */
export function resetAllMockRepositories(repositories: ReturnType<typeof createMockRepositories>) {
  repositories.userRepository.__reset()
  repositories.voterRepository.__reset()
  repositories.stationRepository.__reset()
  repositories.sectionRepository.__reset()
}
