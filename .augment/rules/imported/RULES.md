---
type: 'always_apply'
description: 'Example description'
---

# AI Agent Instructions for Electixir VMS

## AI Coding Agent Workflow: Context7 Integration

**Leveraging my tools:**

1. Use context7 for up-to-date library information:
   - When libraries are used (e.g., "TanStack Query", "Drizzle ORM" etc.), fetch the latest documentation to ensure the generated code is current.
   - Never hallucinate APIs—verify all code suggestions against current docs via Context7.

## Source of Truth Workflow

- **All coding work must strictly follow the workflow defined in:**
  - `docs/requirements.md` (what \& why)
  - `docs/design.md` (architecture, patterns, rationale)
  - `docs/tasks.md` (implementation plan, each task references requirements)
- **No code should be written unless it maps to a documented task and requirement.**
- If unsure about mapping or intent, always ask the human for clarification.

## Coding Standards \& Schema

- **Style, structure, and conventions:**
  - Follow `docs/coding-standards.md` for naming, layering, TDD, and error handling.
- **Database schema:**
  - Stay true to `docs/database-design.mmd` for all data model and schema changes.

## Key Practices

- **Repository/Service/Component boundaries:**
  - No direct DB access outside repositories.
  - All business logic in services, not repositories or components.
- **Type Safety:**
  - Strict TypeScript, no `any` types.
- **Validation:**
  - All input/output must be validated with Zod schemas.
- **Testing:**
  - TDD-first: write tests before implementation, use Arrange-Act-Assert, and test data factories.
- **IPC:**
  - All cross-process calls use type-safe, Zod-validated IPC channels.
- **Import Paths:**
  - **ALWAYS use `@/` alias for all imports from the `src/` directory**
  - Never use relative imports like `../` or `./` when importing from src/
  - Test files outside src/ should use relative imports to src/
  - Example: `import { UserService } from '@/services/userService'`

## Developer Workflows

- Use only the following commands for core workflows:
  - Install \& Run: `pnpm install && pnpm dev`
  - Build: `pnpm build`
  - Test: `pnpm test`
  - E2E: `pnpm e2e`
  - Lint/Format: `pnpm lint`, `pnpm format`
  - Coverage: `pnpm coverage`

## Quality Gates

- All code must pass: TypeScript, ESLint, Prettier, tests, and coverage thresholds before merge.
- Accessibility (WCAG 2.1 AA), performance, and security are enforced in CI.

---

**Summary:**

> AI agents should leverage Context7 MCP, and strictly adhere to documented requirements, coding standards, and workflows—ensuring all code is validated, tested, and mapped to approved tasks before merging.
