#!/usr/bin/env tsx

import { readFileSync } from 'fs'
import { parse } from 'csv-parse/sync'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import Database from 'better-sqlite3'
import { VoterRepository } from '../src/repositories/voterRepository'
import { SectionRepository } from '../src/repositories/sectionRepository'
import { StationRepository } from '../src/repositories/stationRepository'
import { VoterService } from '../src/services/voterService'
import { SectionService } from '../src/services/sectionService'
import { StationService } from '../src/services/stationService'
import { MigrationRunner } from '../src/db/migrationRunner'

interface CSVVoterRow {
  name: string
  relation_type: string
  relation_name?: string
  house_number: string
  birth_year: string
  gender: string
  epic_number: string
  polling_station: string // "55 - LIARBANG" format
  section: string // "1-LIARBANG" or "Unassigned" format
}

interface ParsedStationSection {
  stationCode: string
  stationName: string
  sectionCode: string
  sectionName: string
}

class CSVImporter {
  private db: ReturnType<typeof drizzle>
  private voterService: VoterService
  private sectionService: SectionService
  private stationService: StationService

  private sqlite: Database.Database

  constructor(dbPath: string) {
    this.sqlite = new Database(dbPath)
    this.db = drizzle(this.sqlite)

    // Initialize repositories
    const voterRepo = new VoterRepository(this.db)
    const sectionRepo = new SectionRepository(this.db)
    const stationRepo = new StationRepository(this.db)

    // Initialize services
    this.voterService = new VoterService(voterRepo)
    this.sectionService = new SectionService(sectionRepo)
    this.stationService = new StationService(stationRepo)
  }

  /**
   * Parse polling station and section from CSV format
   * Examples:
   * - polling_station: "55 - LIARBANG" -> code: "55", name: "LIARBANG"
   * - section: "1-LIARBANG" -> code: "1", name: "LIARBANG"
   * - section: "Unassigned" -> code: "UNASSIGNED", name: "Unassigned"
   */
  private parseStationSection(
    pollingStation: string,
    section: string
  ): ParsedStationSection {
    // Parse polling station: "55 - LIARBANG"
    const stationMatch = pollingStation.match(/^(\d+)\s*-\s*(.+)$/)
    if (!stationMatch) {
      throw new Error(`Invalid polling station format: ${pollingStation}`)
    }
    const stationCode = stationMatch[1]
    const stationName = stationMatch[2].trim()

    // Parse section: "1-LIARBANG" or "Unassigned"
    let sectionCode: string
    let sectionName: string

    if (section.toLowerCase() === 'unassigned') {
      sectionCode = 'UNASSIGNED'
      sectionName = 'Unassigned'
    } else {
      const sectionMatch = section.match(/^(\d+)-(.+)$/)
      if (!sectionMatch) {
        throw new Error(`Invalid section format: ${section}`)
      }
      sectionCode = sectionMatch[1]
      sectionName = sectionMatch[2].trim()
    }

    return {
      stationCode,
      stationName,
      sectionCode,
      sectionName,
    }
  }

  async importFromCSV(csvPath: string): Promise<void> {
    console.log(`🚀 Starting CSV import from: ${csvPath}`)

    // Run migrations first
    console.log('📦 Running database migrations...')
    const migrationRunner = new MigrationRunner(this.sqlite)
    await migrationRunner.runMigrations()

    // Read and parse CSV
    console.log('📖 Reading CSV file...')
    const csvContent = readFileSync(csvPath, 'utf-8')
    const records: CSVVoterRow[] = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
    })

    console.log(`📊 Found ${records.length} records to import`)

    // Track unique stations and sections
    const stationsMap = new Map<string, string>() // code -> id
    const sectionsMap = new Map<string, string>() // stationCode:sectionCode -> id

    // First pass: Create polling stations and sections
    console.log('🏢 Creating polling stations and sections...')
    for (const record of records) {
      try {
        const parsed = this.parseStationSection(
          record.polling_station,
          record.section
        )

        // Create polling station if not exists
        if (!stationsMap.has(parsed.stationCode)) {
          try {
            const station = await this.stationService.createStation({
              stationName: parsed.stationName,
              stationNumber: parsed.stationCode,
            })
            stationsMap.set(parsed.stationCode, station.id)
            console.log(
              `✅ Created polling station: ${parsed.stationName} (${parsed.stationCode})`
            )
          } catch (error) {
            // Station might already exist, try to find it
            const existing = await this.stationService.getStationByNumber(
              parsed.stationCode
            )
            if (existing) {
              stationsMap.set(parsed.stationCode, existing.id)
              console.log(
                `🔄 Found existing polling station: ${parsed.stationName} (${parsed.stationCode})`
              )
            } else {
              console.error(
                `❌ Failed to create polling station: ${parsed.stationName}`,
                error
              )
              continue
            }
          }
        }

        // Create section if not exists
        const sectionKey = `${parsed.stationCode}:${parsed.sectionCode}`
        if (!sectionsMap.has(sectionKey)) {
          const pollingStationId = stationsMap.get(parsed.stationCode)!
          try {
            const section = await this.sectionService.createSection({
              stationId: pollingStationId,
              sectionName: parsed.sectionName,
              sectionNumber: parsed.sectionCode,
            })
            sectionsMap.set(sectionKey, section.id)
            console.log(
              `✅ Created section: ${parsed.sectionName} (${parsed.sectionCode})`
            )
          } catch (error) {
            // Section might already exist, try to find it
            const existing = await this.sectionService.findByNumber(
              pollingStationId,
              parsed.sectionCode
            )
            if (existing) {
              sectionsMap.set(sectionKey, existing.id)
              console.log(
                `🔄 Found existing section: ${parsed.sectionName} (${parsed.sectionCode})`
              )
            } else {
              console.error(
                `❌ Failed to create section: ${parsed.sectionName}`,
                error
              )
              continue
            }
          }
        }
      } catch (error) {
        console.error(
          `❌ Failed to parse station/section for record:`,
          record,
          error
        )
        continue
      }
    }

    console.log(
      `🏢 Created/found ${stationsMap.size} polling stations and ${sectionsMap.size} sections`
    )

    // Second pass: Import voters
    console.log('👥 Importing voters...')
    let processed = 0
    let succeeded = 0
    let failed = 0
    const errors: Array<{ row: number; error: string }> = []

    for (let i = 0; i < records.length; i++) {
      const record = records[i]
      const rowNumber = i + 2 // +2 because CSV is 1-indexed and has header

      try {
        const parsed = this.parseStationSection(
          record.polling_station,
          record.section
        )
        const pollingStationId = stationsMap.get(parsed.stationCode)
        const sectionKey = `${parsed.stationCode}:${parsed.sectionCode}`
        const sectionId = sectionsMap.get(sectionKey)

        if (!pollingStationId || !sectionId) {
          throw new Error(
            `Missing polling station or section for row ${rowNumber}`
          )
        }

        await this.voterService.createVoter({
          stationId: pollingStationId,
          sectionId,
          name: record.name.trim(),
          epicNumber: record.epic_number.trim(),
          houseNumber: record.house_number.trim(),
          birthYear: parseInt(record.birth_year),
          gender: record.gender.trim(),
          relationType: record.relation_type.trim(),
          relationName: record.relation_name?.trim(),
          status: 'Active', // Default status since not in CSV
          // Optional fields not in your CSV - set to undefined
          phone: undefined,
          email: undefined,
          facebook: undefined,
          instagram: undefined,
          twitter: undefined,
          supporterStatus: undefined,
          education: undefined,
          occupation: undefined,
          community: undefined,
          religion: undefined,
          economicStatus: undefined,
          customNotes: undefined,
        })

        succeeded++

        // Progress indicator
        if (processed % 100 === 0) {
          console.log(
            `📈 Progress: ${processed}/${records.length} (${Math.round((processed / records.length) * 100)}%)`
          )
        }
      } catch (error) {
        failed++
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error'
        errors.push({ row: rowNumber, error: errorMessage })

        if (failed <= 10) {
          // Only log first 10 errors to avoid spam
          console.error(`❌ Row ${rowNumber}: ${errorMessage}`)
        }
      }

      processed++
    }

    // Final report
    console.log('\n📊 Import Summary:')
    console.log(`✅ Total processed: ${processed}`)
    console.log(`✅ Succeeded: ${succeeded}`)
    console.log(`❌ Failed: ${failed}`)

    if (errors.length > 0) {
      console.log('\n❌ Errors:')
      errors.slice(0, 20).forEach(({ row, error }) => {
        console.log(`   Row ${row}: ${error}`)
      })

      if (errors.length > 20) {
        console.log(`   ... and ${errors.length - 20} more errors`)
      }
    }

    console.log('\n🎉 Import completed!')
  }

  async generateSampleCSV(outputPath: string, rows = 100): Promise<void> {
    console.log(`📝 Generating sample CSV with ${rows} rows...`)

    const sampleData: CSVVoterRow[] = []
    const stations = [
      { code: '55', name: 'LIARBANG' },
      { code: '56', name: 'MAWKYRWAT' },
      { code: '57', name: 'NONGSTOIN' },
    ]
    const sections = [
      { code: '1', name: 'LIARBANG' },
      { code: '2', name: 'LUMBLEI' },
      { code: '3', name: 'MAWSHRIEH' },
      { code: '4', name: 'LIARBANG THIAHMIET' },
    ]
    const genders = ['Male', 'Female']
    const relationTypes = ['Father', 'Mother', 'Husband', 'Others']

    for (let i = 0; i < rows; i++) {
      const station = stations[i % stations.length]
      const section = sections[i % sections.length]
      const birthYear = 1950 + Math.floor(Math.random() * 50)

      sampleData.push({
        name: `VOTER ${String(i + 1).padStart(4, '0')}`,
        relation_type:
          relationTypes[Math.floor(Math.random() * relationTypes.length)],
        relation_name: Math.random() > 0.3 ? `RELATION ${i + 1}` : '',
        house_number: String(Math.floor(Math.random() * 50) + 1),
        birth_year: birthYear.toString(),
        gender: genders[Math.floor(Math.random() * genders.length)],
        epic_number: `${['DHJ', 'RZN', 'IGG'][Math.floor(Math.random() * 3)]}${String(Math.floor(Math.random() * 9000000) + 1000000).padStart(7, '0')}`,
        polling_station: `${station.code} - ${station.name}`,
        section:
          Math.random() > 0.1
            ? `${section.code}-${section.name}`
            : 'Unassigned',
      })
    }

    // Convert to CSV
    const headers = Object.keys(sampleData[0]).join(',')
    const csvRows = sampleData.map((row) =>
      Object.values(row)
        .map((value) =>
          typeof value === 'string' && value.includes(',')
            ? `"${value}"`
            : value || ''
        )
        .join(',')
    )

    const csvContent = [headers, ...csvRows].join('\n')

    // Write to file
    const fs = await import('fs')
    fs.writeFileSync(outputPath, csvContent, 'utf-8')

    console.log(`✅ Sample CSV generated: ${outputPath}`)
    console.log(
      `📊 Contains ${rows} voter records across ${stations.length} stations and ${sections.length} sections`
    )
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)
  const command = args[0]

  if (!command) {
    console.log(`
🗳️  Electixir CSV Import Tool

Usage:
  npm run csv:import <csv-file>     Import voters from CSV file
  npm run csv:sample <output-file>  Generate sample CSV file
  npm run csv:sample <output-file> <rows>  Generate sample CSV with specific row count

Examples:
  npm run csv:import docs/sample-1.csv
  npm run csv:sample sample-voters.csv
  npm run csv:sample sample-voters.csv 1000

CSV Format Expected:
  name,relation_type,relation_name,house_number,birth_year,gender,epic_number,polling_station,section

  Where:
  - polling_station: "55 - LIARBANG" (code - name format)
  - section: "1-LIARBANG" or "Unassigned" (code-name or Unassigned)
`)
    process.exit(1)
  }

  const dbPath = './.data/dev.sqlite'
  const importer = new CSVImporter(dbPath)

  try {
    if (command === 'import') {
      const csvPath = args[1]
      if (!csvPath) {
        console.error('❌ Please provide CSV file path')
        process.exit(1)
      }
      await importer.importFromCSV(csvPath)
    } else if (command === 'sample') {
      const outputPath = args[1] || 'sample-voters.csv'
      const rows = parseInt(args[2]) || 100
      await importer.generateSampleCSV(outputPath, rows)
    } else {
      console.error(`❌ Unknown command: ${command}`)
      process.exit(1)
    }
  } catch (error) {
    console.error('💥 Import failed:', error)
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
